{"time":"2025-07-29 13:42:59.311","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"java.lang.IllegalArgumentException: Policy not found: 1
	at com.imile.stsserver.service.impl.PolicyServiceImpl.resolvePolicy(PolicyServiceImpl.java:216)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.imile.stsserver.service.impl.PolicyServiceImpl$$SpringCGLIB$$0.resolvePolicy(<generated>)
	at com.imile.stsserver.service.impl.AppTokenServerServiceImpl.getStsToken(AppTokenServerServiceImpl.java:63)
	at com.imile.stsserver.controller.AppTokenServerController.generateNewToken(AppTokenServerController.java:259)
	at com.imile.stsserver.controller.AppTokenServerController.processStsTokenRequest(AppTokenServerController.java:228)
	at com.imile.stsserver.controller.AppTokenServerController.lambda$getStsToken$0(AppTokenServerController.java:153)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"92","traceId":"","appName":"AppTokenServer","message":"STS assume role failed with unexpected error: Policy not found: 1"}
{"time":"2025-07-29 13:42:59.313","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"com.imile.stsserver.exception.StsException: Failed to assume role: Policy not found: 1
	at com.imile.stsserver.service.impl.AppTokenServerServiceImpl.getStsToken(AppTokenServerServiceImpl.java:93)
	at com.imile.stsserver.controller.AppTokenServerController.generateNewToken(AppTokenServerController.java:259)
	at com.imile.stsserver.controller.AppTokenServerController.processStsTokenRequest(AppTokenServerController.java:228)
	at com.imile.stsserver.controller.AppTokenServerController.lambda$getStsToken$0(AppTokenServerController.java:153)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: java.lang.IllegalArgumentException: Policy not found: 1
	at com.imile.stsserver.service.impl.PolicyServiceImpl.resolvePolicy(PolicyServiceImpl.java:216)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.imile.stsserver.service.impl.PolicyServiceImpl$$SpringCGLIB$$0.resolvePolicy(<generated>)
	at com.imile.stsserver.service.impl.AppTokenServerServiceImpl.getStsToken(AppTokenServerServiceImpl.java:63)
	... 10 common frames omitted
","class":"com.imile.stsserver.controller.AppTokenServerController","line":"317","traceId":"","appName":"AppTokenServer","message":"=== STS Service Error - RequestId: REQ-1753767777256-93, Total Time: 2057ms, ErrorCode: UNKNOWN_ERROR, Message: Failed to assume role: Policy not found: 1 ==="}
{"time":"2025-07-29 13:42:59.335","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"109","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"Request completed with server error - Status: 500"}
{"time":"2025-07-29 13:58:36.099","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"org.springframework.data.redis.serializer.SerializationException: Could not write JSON: Java 8 date/time type `java.time.LocalDateTime` not supported by default: add Module \"com.fasterxml.jackson.datatype:jackson-datatype-jsr310\" to enable handling (through reference chain: com.imile.stsserver.entity.AliyunAk[\"createdate\"])
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.serialize(GenericJackson2JsonRedisSerializer.java:274)
	at org.springframework.data.redis.serializer.DefaultRedisElementWriter.write(DefaultRedisElementWriter.java:42)
	at org.springframework.data.redis.serializer.RedisSerializationContext$SerializationPair.write(RedisSerializationContext.java:292)
	at org.springframework.data.redis.cache.RedisCache.serializeCacheValue(RedisCache.java:343)
	at org.springframework.data.redis.cache.RedisCache.put(RedisCache.java:204)
	at org.springframework.cache.interceptor.AbstractCacheInvoker.doPut(AbstractCacheInvoker.java:157)
	at org.springframework.cache.interceptor.CacheAspectSupport$CachePutRequest.performCachePut(CacheAspectSupport.java:1087)
	at org.springframework.cache.interceptor.CacheAspectSupport$CachePutRequest.apply(CacheAspectSupport.java:1072)
	at org.springframework.cache.interceptor.CacheAspectSupport.evaluate(CacheAspectSupport.java:616)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:448)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:410)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:65)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.imile.stsserver.service.impl.AliyunAkServiceImpl$$SpringCGLIB$$0.getByTokenAndEnabled(<generated>)
	at com.imile.stsserver.controller.AppTokenServerController.getConfigWithCache(AppTokenServerController.java:82)
	at com.imile.stsserver.controller.AppTokenServerController.processStsTokenRequest(AppTokenServerController.java:184)
	at com.imile.stsserver.controller.AppTokenServerController.lambda$getStsToken$0(AppTokenServerController.java:153)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Java 8 date/time type `java.time.LocalDateTime` not supported by default: add Module \"com.fasterxml.jackson.datatype:jackson-datatype-jsr310\" to enable handling (through reference chain: com.imile.stsserver.entity.AliyunAk[\"createdate\"])
	at com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:77)
	at com.fasterxml.jackson.databind.SerializerProvider.reportBadDefinition(SerializerProvider.java:1340)
	at com.fasterxml.jackson.databind.ser.impl.UnsupportedTypeSerializer.serialize(UnsupportedTypeSerializer.java:35)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:770)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeWithType(BeanSerializerBase.java:653)
	at com.fasterxml.jackson.databind.ser.impl.TypeWrappedSerializer.serialize(TypeWrappedSerializer.java:32)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:502)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:341)
	at com.fasterxml.jackson.databind.ObjectMapper._writeValueAndClose(ObjectMapper.java:4819)
	at com.fasterxml.jackson.databind.ObjectMapper.writeValueAsBytes(ObjectMapper.java:4085)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.serialize(GenericJackson2JsonRedisSerializer.java:272)
	... 24 common frames omitted
","class":"com.imile.stsserver.config.CacheConfig","line":"121","traceId":"","appName":"AppTokenServer","message":"Cache put error - Cache: aliyun-ak-cache, Key: token:9733af120a0ccd48d5016cf4568258bc"}
{"time":"2025-07-29 14:05:53.567","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"main","stack_trace":"org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'stsClient' defined in class path resource [com/imile/stsserver/config/StsConfig.class]: Failed to instantiate [com.aliyun.sts20150401.Client]: Factory method 'stsClient' threw exception with message: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:489)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.imile.stsserver.StsServerApplication.main(StsServerApplication.java:12)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.aliyun.sts20150401.Client]: Factory method 'stsClient' threw exception with message: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 21 common frames omitted
Caused by: java.lang.IllegalArgumentException: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)
	at com.imile.stsserver.config.StsConfig.stsClient(StsConfig.java:39)
	at com.imile.stsserver.config.StsConfig$$SpringCGLIB$$0.CGLIB$stsClient$0(<generated>)
	at com.imile.stsserver.config.StsConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:400)
	at com.imile.stsserver.config.StsConfig$$SpringCGLIB$$0.stsClient(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 24 common frames omitted
","class":"org.springframework.boot.SpringApplication","line":"857","traceId":"","appName":"AppTokenServer","message":"Application run failed"}
{"time":"2025-07-29 14:19:02.986","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"main","stack_trace":"org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'stsClient' defined in class path resource [com/imile/stsserver/config/StsConfig.class]: Failed to instantiate [com.aliyun.sts20150401.Client]: Factory method 'stsClient' threw exception with message: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:489)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.imile.stsserver.StsServerApplication.main(StsServerApplication.java:12)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.aliyun.sts20150401.Client]: Factory method 'stsClient' threw exception with message: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 21 common frames omitted
Caused by: java.lang.IllegalArgumentException: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)
	at com.imile.stsserver.config.StsConfig.stsClient(StsConfig.java:39)
	at com.imile.stsserver.config.StsConfig$$SpringCGLIB$$0.CGLIB$stsClient$0(<generated>)
	at com.imile.stsserver.config.StsConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:400)
	at com.imile.stsserver.config.StsConfig$$SpringCGLIB$$0.stsClient(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 24 common frames omitted
","class":"org.springframework.boot.SpringApplication","line":"857","traceId":"","appName":"AppTokenServer","message":"Application run failed"}
{"time":"2025-07-29 15:05:07.745","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"main","stack_trace":"org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'stsClient' defined in class path resource [com/imile/stsserver/config/StsConfig.class]: Failed to instantiate [com.aliyun.sts20150401.Client]: Factory method 'stsClient' threw exception with message: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:489)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.imile.stsserver.StsServerApplication.main(StsServerApplication.java:12)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.aliyun.sts20150401.Client]: Factory method 'stsClient' threw exception with message: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 21 common frames omitted
Caused by: java.lang.IllegalArgumentException: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)
	at com.imile.stsserver.config.StsConfig.stsClient(StsConfig.java:39)
	at com.imile.stsserver.config.StsConfig$$SpringCGLIB$$0.CGLIB$stsClient$0(<generated>)
	at com.imile.stsserver.config.StsConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:400)
	at com.imile.stsserver.config.StsConfig$$SpringCGLIB$$0.stsClient(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171)
	... 24 common frames omitted
","class":"org.springframework.boot.SpringApplication","line":"857","traceId":"","appName":"AppTokenServer","message":"Application run failed"}
{"time":"2025-07-29 15:07:45.722","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"org.springframework.data.redis.serializer.SerializationException: Could not write JSON: Java 8 date/time type `java.time.LocalDateTime` not supported by default: add Module \"com.fasterxml.jackson.datatype:jackson-datatype-jsr310\" to enable handling (through reference chain: com.imile.stsserver.entity.AliyunAk[\"createdate\"])
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.serialize(GenericJackson2JsonRedisSerializer.java:274)
	at org.springframework.data.redis.serializer.DefaultRedisElementWriter.write(DefaultRedisElementWriter.java:42)
	at org.springframework.data.redis.serializer.RedisSerializationContext$SerializationPair.write(RedisSerializationContext.java:292)
	at org.springframework.data.redis.cache.RedisCache.serializeCacheValue(RedisCache.java:343)
	at org.springframework.data.redis.cache.RedisCache.put(RedisCache.java:204)
	at org.springframework.cache.interceptor.AbstractCacheInvoker.doPut(AbstractCacheInvoker.java:157)
	at org.springframework.cache.interceptor.CacheAspectSupport$CachePutRequest.performCachePut(CacheAspectSupport.java:1087)
	at org.springframework.cache.interceptor.CacheAspectSupport$CachePutRequest.apply(CacheAspectSupport.java:1072)
	at org.springframework.cache.interceptor.CacheAspectSupport.evaluate(CacheAspectSupport.java:616)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:448)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:410)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:65)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.imile.stsserver.service.impl.AliyunAkServiceImpl$$SpringCGLIB$$0.getByTokenAndEnabled(<generated>)
	at com.imile.stsserver.controller.AppTokenServerController.getConfigWithCache(AppTokenServerController.java:82)
	at com.imile.stsserver.controller.AppTokenServerController.processStsTokenRequest(AppTokenServerController.java:184)
	at com.imile.stsserver.controller.AppTokenServerController.lambda$getStsToken$0(AppTokenServerController.java:153)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Java 8 date/time type `java.time.LocalDateTime` not supported by default: add Module \"com.fasterxml.jackson.datatype:jackson-datatype-jsr310\" to enable handling (through reference chain: com.imile.stsserver.entity.AliyunAk[\"createdate\"])
	at com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:77)
	at com.fasterxml.jackson.databind.SerializerProvider.reportBadDefinition(SerializerProvider.java:1340)
	at com.fasterxml.jackson.databind.ser.impl.UnsupportedTypeSerializer.serialize(UnsupportedTypeSerializer.java:35)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:770)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeWithType(BeanSerializerBase.java:653)
	at com.fasterxml.jackson.databind.ser.impl.TypeWrappedSerializer.serialize(TypeWrappedSerializer.java:32)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:502)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:341)
	at com.fasterxml.jackson.databind.ObjectMapper._writeValueAndClose(ObjectMapper.java:4819)
	at com.fasterxml.jackson.databind.ObjectMapper.writeValueAsBytes(ObjectMapper.java:4085)
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.serialize(GenericJackson2JsonRedisSerializer.java:272)
	... 24 common frames omitted
","class":"com.imile.stsserver.config.CacheConfig","line":"121","traceId":"","appName":"AppTokenServer","message":"Cache put error - Cache: aliyun-ak-cache, Key: token:9733af120a0ccd48d5016cf4568258bc"}
{"time":"2025-07-29 16:14:39.237","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.exception.GlobalExceptionHandler","line":"144","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"=== Global Exception Handler - General Exception ==="}
{"time":"2025-07-29 16:14:39.239","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.exception.GlobalExceptionHandler","line":"145","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"Unexpected error occurred - Type: NoResourceFoundException, Message: No static resource ."}
{"time":"2025-07-29 16:14:39.239","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-9","stack_trace":"org.springframework.web.servlet.resource.NoResourceFoundException: No static resource .
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
","class":"com.imile.stsserver.exception.GlobalExceptionHandler","line":"147","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"General exception stack trace:"}
{"time":"2025-07-29 16:14:39.243","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.exception.GlobalExceptionHandler","line":"154","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"Returning general error response: {success=false, errorMessage=Internal server error occurred, errorCode=INTERNAL_ERROR}"}
{"time":"2025-07-29 16:14:39.244","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.exception.GlobalExceptionHandler","line":"155","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"=== General Exception Handling Completed ==="}
{"time":"2025-07-29 16:14:39.256","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"109","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"Request completed with server error - Status: 500"}
{"time":"2025-07-29 16:14:39.570","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.exception.GlobalExceptionHandler","line":"144","traceId":"","appName":"AppTokenServer","message":"=== Global Exception Handler - General Exception ==="}
{"time":"2025-07-29 16:14:39.570","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.exception.GlobalExceptionHandler","line":"145","traceId":"","appName":"AppTokenServer","message":"Unexpected error occurred - Type: NoResourceFoundException, Message: No static resource favicon.ico."}
{"time":"2025-07-29 16:14:39.570","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-6","stack_trace":"org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
","class":"com.imile.stsserver.exception.GlobalExceptionHandler","line":"147","traceId":"","appName":"AppTokenServer","message":"General exception stack trace:"}
{"time":"2025-07-29 16:14:39.571","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.exception.GlobalExceptionHandler","line":"154","traceId":"","appName":"AppTokenServer","message":"Returning general error response: {success=false, errorMessage=Internal server error occurred, errorCode=INTERNAL_ERROR}"}
{"time":"2025-07-29 16:14:39.571","ip":"DESKTOP-G92L71C","level":"ERROR","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.exception.GlobalExceptionHandler","line":"155","traceId":"","appName":"AppTokenServer","message":"=== General Exception Handling Completed ==="}
