{"time":"2025-07-29 12:27:20.276","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 12:27:20.313","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 70496 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 12:27:20.314","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 12:27:20.998","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 12:27:21.000","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 12:27:21.024","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 12:27:21.500","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 12:27:21.509","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 12:27:21.510","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 12:27:21.510","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 12:27:21.561","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 12:27:21.561","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1216 ms"}
{"time":"2025-07-29 12:27:22.052","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 12:27:22.056","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 12:27:22.057","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 12:27:22.059","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 12:27:22.541","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 12:27:22.577","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 12:27:22.589","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 12:27:22.599","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.857 seconds (process running for 3.519)"}
{"time":"2025-07-29 12:27:22.603","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.service.DataInitializationService","line":"24","traceId":"","appName":"AppTokenServer","message":"=== Data Initialization Started ==="}
{"time":"2025-07-29 12:27:22.614","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Starting..."}
{"time":"2025-07-29 12:27:27.343","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@299dd381"}
{"time":"2025-07-29 12:27:27.349","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Start completed."}
{"time":"2025-07-29 12:27:27.404","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.service.DataInitializationService","line":"30","traceId":"","appName":"AppTokenServer","message":"Found 1 enabled Aliyun AK configurations, skipping initialization"}
{"time":"2025-07-29 12:27:27.596","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 12:27:27.598","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 12:27:27.598","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 0 ms"}
{"time":"2025-07-29 12:28:26.020","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 12:28:26.020","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: cc02054b9f8042b583416cc0d8881d9a (Generated)"}
{"time":"2025-07-29 12:28:26.020","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 12:28:26.022","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 12:28:26.022","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 12:28:26.022","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:28:26.022","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"  request-start-time: 1753763306017"}
{"time":"2025-07-29 12:28:26.022","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"  content-length: 85"}
{"time":"2025-07-29 12:28:26.022","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 12:28:26.022","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 12:28:26.022","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 12:28:26.022","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"cc02054b9f8042b583416cc0d8881d9a","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:28:26.082","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 12:28:26.083","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753763306081-102 ==="}
{"time":"2025-07-29 12:28:26.083","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 12:28:26.117","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"329","traceId":"","appName":"AppTokenServer","message":"AES decrypting Aliyun configuration"}
{"time":"2025-07-29 12:28:26.129","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"336","traceId":"","appName":"AppTokenServer","message":"Aliyun configuration AES decryption completed - AccessKeyID length: 24, AccessKeySecret length: 30, RoleArn length: 41"}
{"time":"2025-07-29 12:28:26.130","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.entity.AliyunAk","line":"148","traceId":"","appName":"AppTokenServer","message":"Successfully decrypted Aliyun AK configuration - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ"}
{"time":"2025-07-29 12:28:26.130","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AliyunAkServiceImpl","line":"98","traceId":"","appName":"AppTokenServer","message":"Retrieved and decrypted enabled Aliyun AK configuration by token - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ, Status: 1"}
{"time":"2025-07-29 12:28:26.130","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 47ms - RequestId: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:27.466","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 1335ms - RequestId: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:27.466","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"227","traceId":"","appName":"AppTokenServer","message":"Cache miss - Generating new token - RequestId: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:27.466","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"253","traceId":"","appName":"AppTokenServer","message":"Protocol validation time: 0ms - RequestId: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:27.466","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"257","traceId":"","appName":"AppTokenServer","message":"Calling STS service - RequestId: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:27.466","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"169","traceId":"","appName":"AppTokenServer","message":"Generated new session name: sts-MR6hoQtv-20250729122827"}
{"time":"2025-07-29 12:28:27.467","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"46","traceId":"","appName":"AppTokenServer","message":"Starting STS assume role operation - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-MR6hoQtv-20250729122827 (auto-generated: true), Duration: 3600s, Protocol: HTTPS"}
{"time":"2025-07-29 12:28:27.467","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.PolicyService","line":"59","traceId":"","appName":"AppTokenServer","message":"Reading policy from classpath: policies/write_policy.txt"}
{"time":"2025-07-29 12:28:27.467","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"69","traceId":"","appName":"AppTokenServer","message":"Applied policy from: file write_policy.txt"}
{"time":"2025-07-29 12:28:27.884","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"83","traceId":"","appName":"AppTokenServer","message":"STS assume role operation completed successfully - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-MR6hoQtv-20250729122827"}
{"time":"2025-07-29 12:28:27.885","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"271","traceId":"","appName":"AppTokenServer","message":"STS service call time: 419ms - RequestId: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:27.885","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"272","traceId":"","appName":"AppTokenServer","message":"STS token generated successfully - StatusCode: 200, AccessKeyId: STS.NXg4***, Expiration: 2025-07-29T13:28:28Z - RequestId: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:27.885","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"291","traceId":"","appName":"AppTokenServer","message":"Cache async setup time: 0ms - RequestId: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:27.885","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"300","traceId":"","appName":"AppTokenServer","message":"Response header creation time: 0ms - RequestId: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:27.885","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"304","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Completed Successfully - RequestId: REQ-1753763306081-102, Total Time: 1804ms ==="}
{"time":"2025-07-29 12:28:27.885","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"305","traceId":"","appName":"AppTokenServer","message":"Time breakdown - Config: N/Ams, Cache Check: N/Ams, STS Call: 419ms, Total: 1804ms - RequestId: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 809b4a312eac4c2e8e86185738650832 (Generated)"}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  request-start-time: 1753763306017"}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  content-length: 85"}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 12:28:27.894","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: 809b4a312eac4c2e8e86185738650832"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  X-Trace-ID: 809b4a312eac4c2e8e86185738650832"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  X-Cache-Status: MISS"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:d316d38aad7d237e692cbbfdbce5c47a"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3600"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  X-Response-Time: 1753763307885"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  X-STS-Call-Time: 419"}
{"time":"2025-07-29 12:28:27.898","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 12:28:27.900","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 12:28:27.900","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 04:28:27 GMT"}
{"time":"2025-07-29 12:28:27.900","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 12:28:27.900","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 12:28:27.900","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 12:28:27.900","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 12:28:27.900","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"809b4a312eac4c2e8e86185738650832","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 809b4a312eac4c2e8e86185738650832 ==="}
{"time":"2025-07-29 12:28:27.913","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"143","traceId":"","appName":"AppTokenServer","message":"Successfully cached STS token with key: sts:token:v1:d316d38aad7d237e692cbbfdbce5c47a for 600 seconds"}
{"time":"2025-07-29 12:28:27.913","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"285","traceId":"","appName":"AppTokenServer","message":"Async cache put time: 28ms - RequestId: REQ-1753763306081-102"}
{"time":"2025-07-29 12:28:37.513","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 12:28:37.513","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 3b1b2628033b41b99bde2931d92d33ee (Generated)"}
{"time":"2025-07-29 12:28:37.513","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 12:28:37.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 12:28:37.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 12:28:37.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:28:37.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"  request-start-time: 1753763317512"}
{"time":"2025-07-29 12:28:37.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"  content-length: 85"}
{"time":"2025-07-29 12:28:37.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 12:28:37.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 12:28:37.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 12:28:37.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"3b1b2628033b41b99bde2931d92d33ee","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:28:37.515","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 12:28:37.515","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753763317515-116 ==="}
{"time":"2025-07-29 12:28:37.515","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 12:28:37.515","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 0ms - RequestId: REQ-1753763317515-116"}
{"time":"2025-07-29 12:28:37.529","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"111","traceId":"","appName":"AppTokenServer","message":"Cache hit for key: sts:token:v1:d316d38aad7d237e692cbbfdbce5c47a - Retrieved valid STS token from cache, expires at: 2025-07-29T13:28:28Z"}
{"time":"2025-07-29 12:28:37.529","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 14ms - RequestId: REQ-1753763317515-116"}
{"time":"2025-07-29 12:28:37.529","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"221","traceId":"","appName":"AppTokenServer","message":"=== Cache Hit - Request Completed - RequestId: REQ-1753763317515-116, Total Time: 14ms ==="}
{"time":"2025-07-29 12:28:37.531","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 12:28:37.531","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: ff7101ac2fd8471e9842ba059ecacdd9 (Generated)"}
{"time":"2025-07-29 12:28:37.531","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 12:28:37.531","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 12:28:37.531","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 12:28:37.531","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:28:37.531","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  request-start-time: 1753763317512"}
{"time":"2025-07-29 12:28:37.531","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  content-length: 85"}
{"time":"2025-07-29 12:28:37.531","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 12:28:37.532","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 12:28:37.532","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 12:28:37.532","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: ff7101ac2fd8471e9842ba059ecacdd9"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  X-Trace-ID: ff7101ac2fd8471e9842ba059ecacdd9"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  X-Cache-Status: HIT"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:d316d38aad7d237e692cbbfdbce5c47a"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3590"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  X-Response-Time: 1753763317529"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753763317515-116"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 04:28:37 GMT"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 12:28:37.533","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"ff7101ac2fd8471e9842ba059ecacdd9","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: ff7101ac2fd8471e9842ba059ecacdd9 ==="}
{"time":"2025-07-29 12:29:45.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 12:29:45.421","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 12:29:45.441","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown initiated..."}
{"time":"2025-07-29 12:29:45.446","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown completed."}
{"time":"2025-07-29 12:31:32.196","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 12:31:32.233","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 83532 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 12:31:32.234","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 12:31:32.823","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 12:31:32.824","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 12:31:32.847","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 12:31:33.266","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 12:31:33.275","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 12:31:33.276","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 12:31:33.277","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 12:31:33.326","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 12:31:33.326","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1066 ms"}
{"time":"2025-07-29 12:31:33.748","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 12:31:33.752","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 12:31:33.754","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 12:31:33.756","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 12:31:34.197","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 12:31:34.235","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 12:31:34.247","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 12:31:34.257","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.439 seconds (process running for 2.883)"}
{"time":"2025-07-29 12:31:34.261","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.service.DataInitializationService","line":"24","traceId":"","appName":"AppTokenServer","message":"=== Data Initialization Started ==="}
{"time":"2025-07-29 12:31:34.271","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Starting..."}
{"time":"2025-07-29 12:31:38.974","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4601a148"}
{"time":"2025-07-29 12:31:38.989","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Start completed."}
{"time":"2025-07-29 12:31:39.039","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.service.DataInitializationService","line":"30","traceId":"","appName":"AppTokenServer","message":"Found 1 enabled Aliyun AK configurations, skipping initialization"}
{"time":"2025-07-29 12:31:39.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 12:31:39.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 12:31:39.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 2 ms"}
{"time":"2025-07-29 12:32:22.276","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 12:32:22.489","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 12:32:22.507","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown initiated..."}
{"time":"2025-07-29 12:32:22.511","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown completed."}
{"time":"2025-07-29 12:32:26.994","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 12:32:27.027","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 83608 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 12:32:27.028","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 12:32:27.684","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 12:32:27.686","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 12:32:27.706","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 12:32:28.144","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 12:32:28.153","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 12:32:28.154","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 12:32:28.154","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 12:32:28.203","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 12:32:28.203","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1143 ms"}
{"time":"2025-07-29 12:32:28.713","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 12:32:28.721","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 12:32:28.726","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 12:32:28.728","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 12:32:29.225","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 12:32:29.262","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 12:32:29.274","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 12:32:29.283","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.615 seconds (process running for 2.964)"}
{"time":"2025-07-29 12:32:29.522","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 12:32:29.522","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 12:32:29.523","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 1 ms"}
{"time":"2025-07-29 12:32:29.523","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Starting..."}
{"time":"2025-07-29 12:32:34.228","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@12201fbf"}
{"time":"2025-07-29 12:32:34.231","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Start completed."}
{"time":"2025-07-29 12:32:50.923","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 12:32:51.113","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 12:32:51.131","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown initiated..."}
{"time":"2025-07-29 12:32:52.827","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown completed."}
{"time":"2025-07-29 12:35:53.001","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 12:35:53.039","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 52248 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 12:35:53.040","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 12:35:53.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 12:35:53.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 12:35:53.664","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 12:35:54.068","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 12:35:54.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 12:35:54.077","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 12:35:54.078","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 12:35:54.124","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 12:35:54.124","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1053 ms"}
{"time":"2025-07-29 12:35:54.537","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 12:35:54.540","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 12:35:54.541","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 12:35:54.544","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 12:35:54.964","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 12:35:54.997","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 12:35:55.008","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 12:35:55.018","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.398 seconds (process running for 2.749)"}
{"time":"2025-07-29 12:35:55.484","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 12:35:55.484","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 12:35:55.485","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 1 ms"}
{"time":"2025-07-29 12:35:55.489","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Starting..."}
{"time":"2025-07-29 12:36:00.202","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3fea2fe1"}
{"time":"2025-07-29 12:36:00.217","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Start completed."}
{"time":"2025-07-29 12:36:07.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 12:36:07.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 6445ad55412143f49786a8ce72c7138d (Generated)"}
{"time":"2025-07-29 12:36:07.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 12:36:07.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 12:36:07.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 12:36:07.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:36:07.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"  request-start-time: 1753763767818"}
{"time":"2025-07-29 12:36:07.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"  content-length: 85"}
{"time":"2025-07-29 12:36:07.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 12:36:07.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 12:36:07.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 12:36:07.867","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"6445ad55412143f49786a8ce72c7138d","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:36:07.940","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 12:36:07.940","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753763767938-93 ==="}
{"time":"2025-07-29 12:36:07.941","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 12:36:07.996","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"329","traceId":"","appName":"AppTokenServer","message":"AES decrypting Aliyun configuration"}
{"time":"2025-07-29 12:36:08.005","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"336","traceId":"","appName":"AppTokenServer","message":"Aliyun configuration AES decryption completed - AccessKeyID length: 24, AccessKeySecret length: 30, RoleArn length: 41"}
{"time":"2025-07-29 12:36:08.005","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.entity.AliyunAk","line":"148","traceId":"","appName":"AppTokenServer","message":"Successfully decrypted Aliyun AK configuration - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ"}
{"time":"2025-07-29 12:36:08.005","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AliyunAkServiceImpl","line":"98","traceId":"","appName":"AppTokenServer","message":"Retrieved and decrypted enabled Aliyun AK configuration by token - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ, Status: 1"}
{"time":"2025-07-29 12:36:08.005","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 64ms - RequestId: REQ-1753763767938-93"}
{"time":"2025-07-29 12:36:09.287","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"111","traceId":"","appName":"AppTokenServer","message":"Cache hit for key: sts:token:v1:d316d38aad7d237e692cbbfdbce5c47a - Retrieved valid STS token from cache, expires at: 2025-07-29T13:28:28Z"}
{"time":"2025-07-29 12:36:09.287","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 1281ms - RequestId: REQ-1753763767938-93"}
{"time":"2025-07-29 12:36:09.287","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"221","traceId":"","appName":"AppTokenServer","message":"=== Cache Hit - Request Completed - RequestId: REQ-1753763767938-93, Total Time: 1349ms ==="}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: bd078e7e98ba43efb5a038374c8a2af6 (Generated)"}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  request-start-time: 1753763767818"}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  content-length: 85"}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 12:36:09.296","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:36:09.308","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 12:36:09.308","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: bd078e7e98ba43efb5a038374c8a2af6"}
{"time":"2025-07-29 12:36:09.308","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 12:36:09.308","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  X-Trace-ID: bd078e7e98ba43efb5a038374c8a2af6"}
{"time":"2025-07-29 12:36:09.309","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  X-Cache-Status: HIT"}
{"time":"2025-07-29 12:36:09.309","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:d316d38aad7d237e692cbbfdbce5c47a"}
{"time":"2025-07-29 12:36:09.309","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3138"}
{"time":"2025-07-29 12:36:09.309","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  X-Response-Time: 1753763769287"}
{"time":"2025-07-29 12:36:09.309","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-29 12:36:09.309","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-29 12:36:09.309","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753763767938-93"}
{"time":"2025-07-29 12:36:09.310","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 12:36:09.310","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 12:36:09.311","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 04:36:09 GMT"}
{"time":"2025-07-29 12:36:09.311","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 12:36:09.311","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 12:36:09.311","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 12:36:09.312","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 12:36:09.312","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"bd078e7e98ba43efb5a038374c8a2af6","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: bd078e7e98ba43efb5a038374c8a2af6 ==="}
{"time":"2025-07-29 12:36:13.495","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 12:36:13.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 8896f2028e2742a29cb0ef675665fb77 (Generated)"}
{"time":"2025-07-29 12:36:13.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 12:36:13.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 12:36:13.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 12:36:13.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:36:13.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"  request-start-time: 1753763773493"}
{"time":"2025-07-29 12:36:13.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"  content-length: 85"}
{"time":"2025-07-29 12:36:13.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 12:36:13.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 12:36:13.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 12:36:13.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"8896f2028e2742a29cb0ef675665fb77","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:36:13.499","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 12:36:13.499","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753763773499-93 ==="}
{"time":"2025-07-29 12:36:13.499","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 12:36:13.499","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 0ms - RequestId: REQ-1753763773499-93"}
{"time":"2025-07-29 12:36:13.515","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"111","traceId":"","appName":"AppTokenServer","message":"Cache hit for key: sts:token:v1:d316d38aad7d237e692cbbfdbce5c47a - Retrieved valid STS token from cache, expires at: 2025-07-29T13:28:28Z"}
{"time":"2025-07-29 12:36:13.515","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 16ms - RequestId: REQ-1753763773499-93"}
{"time":"2025-07-29 12:36:13.515","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"221","traceId":"","appName":"AppTokenServer","message":"=== Cache Hit - Request Completed - RequestId: REQ-1753763773499-93, Total Time: 16ms ==="}
{"time":"2025-07-29 12:36:13.515","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 12:36:13.516","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 3100fc1e052d47a099d423bcc2267182 (Generated)"}
{"time":"2025-07-29 12:36:13.516","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 12:36:13.516","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 12:36:13.516","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 12:36:13.516","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:36:13.516","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  request-start-time: 1753763773493"}
{"time":"2025-07-29 12:36:13.516","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  content-length: 85"}
{"time":"2025-07-29 12:36:13.516","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 12:36:13.516","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 12:36:13.516","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 12:36:13.516","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 12:36:13.518","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 12:36:13.518","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: 3100fc1e052d47a099d423bcc2267182"}
{"time":"2025-07-29 12:36:13.518","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 12:36:13.518","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  X-Trace-ID: 3100fc1e052d47a099d423bcc2267182"}
{"time":"2025-07-29 12:36:13.518","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  X-Cache-Status: HIT"}
{"time":"2025-07-29 12:36:13.518","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:d316d38aad7d237e692cbbfdbce5c47a"}
{"time":"2025-07-29 12:36:13.518","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3134"}
{"time":"2025-07-29 12:36:13.519","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  X-Response-Time: 1753763773515"}
{"time":"2025-07-29 12:36:13.519","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-29 12:36:13.519","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-29 12:36:13.519","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753763773499-93"}
{"time":"2025-07-29 12:36:13.519","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 12:36:13.519","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 12:36:13.519","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 04:36:13 GMT"}
{"time":"2025-07-29 12:36:13.520","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 12:36:13.520","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 12:36:13.520","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 12:36:13.520","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 12:36:13.520","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"3100fc1e052d47a099d423bcc2267182","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 3100fc1e052d47a099d423bcc2267182 ==="}
{"time":"2025-07-29 12:36:20.788","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 12:36:21.016","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 12:36:21.046","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown initiated..."}
{"time":"2025-07-29 13:39:21.018","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 13:39:21.054","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 68264 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 13:39:21.055","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 13:39:21.808","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 13:39:21.810","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 13:39:21.833","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 13:39:22.278","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 13:39:22.287","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 13:39:22.287","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 13:39:22.288","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 13:39:22.336","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 13:39:22.337","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1250 ms"}
{"time":"2025-07-29 13:39:22.810","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 13:39:22.812","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 13:39:22.814","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 13:39:22.815","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 13:39:23.264","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 13:39:23.307","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 13:39:23.319","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 13:39:23.330","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.696 seconds (process running for 3.184)"}
{"time":"2025-07-29 13:39:23.334","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.service.PolicyInitializationService","line":"25","traceId":"","appName":"AppTokenServer","message":"=== Policy Initialization Started ==="}
{"time":"2025-07-29 13:39:23.345","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Starting..."}
{"time":"2025-07-29 13:39:28.149","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@31f9f9b3"}
{"time":"2025-07-29 13:39:28.162","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Start completed."}
{"time":"2025-07-29 13:39:28.251","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.service.PolicyInitializationService","line":"31","traceId":"","appName":"AppTokenServer","message":"Found 4 existing enabled policies, skipping initialization"}
{"time":"2025-07-29 13:39:28.253","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.service.PolicyInitializationService","line":"32","traceId":"","appName":"AppTokenServer","message":"=== Policy Initialization Skipped ==="}
{"time":"2025-07-29 13:39:28.738","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 13:39:28.738","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 13:39:28.739","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 0 ms"}
{"time":"2025-07-29 13:40:19.406","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 13:40:19.595","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 13:40:19.615","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown initiated..."}
{"time":"2025-07-29 13:40:19.620","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown completed."}
{"time":"2025-07-29 13:40:22.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 13:40:22.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 48088 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 13:40:22.655","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 13:40:23.247","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 13:40:23.248","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 13:40:23.270","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 13:40:23.668","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 13:40:23.678","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 13:40:23.679","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 13:40:23.679","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 13:40:23.724","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 13:40:23.725","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1040 ms"}
{"time":"2025-07-29 13:40:24.152","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 13:40:24.155","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 13:40:24.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 13:40:24.158","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 13:40:24.581","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 13:40:24.621","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 13:40:24.632","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 13:40:24.641","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.349 seconds (process running for 2.705)"}
{"time":"2025-07-29 13:40:25.135","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Starting..."}
{"time":"2025-07-29 13:40:25.163","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 13:40:25.163","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 13:40:25.164","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 1 ms"}
{"time":"2025-07-29 13:40:29.814","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4e5ec2a8"}
{"time":"2025-07-29 13:40:29.828","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Start completed."}
{"time":"2025-07-29 13:42:57.183","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:42:57.183","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 6d8c3350d38341ee8825afee216aa87d (Generated)"}
{"time":"2025-07-29 13:42:57.183","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:42:57.183","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:42:57.183","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:42:57.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:42:57.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"  request-start-time: 1753767777128"}
{"time":"2025-07-29 13:42:57.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"  content-length: 70"}
{"time":"2025-07-29 13:42:57.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:42:57.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:42:57.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:42:57.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"6d8c3350d38341ee8825afee216aa87d","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:42:57.258","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 13:42:57.258","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753767777256-93 ==="}
{"time":"2025-07-29 13:42:57.258","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 13:42:57.307","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"329","traceId":"","appName":"AppTokenServer","message":"AES decrypting Aliyun configuration"}
{"time":"2025-07-29 13:42:57.319","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"336","traceId":"","appName":"AppTokenServer","message":"Aliyun configuration AES decryption completed - AccessKeyID length: 24, AccessKeySecret length: 30, RoleArn length: 41"}
{"time":"2025-07-29 13:42:57.319","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.entity.AliyunAk","line":"148","traceId":"","appName":"AppTokenServer","message":"Successfully decrypted Aliyun AK configuration - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ"}
{"time":"2025-07-29 13:42:57.319","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AliyunAkServiceImpl","line":"98","traceId":"","appName":"AppTokenServer","message":"Retrieved and decrypted enabled Aliyun AK configuration by token - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ, Status: 1"}
{"time":"2025-07-29 13:42:57.319","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 61ms - RequestId: REQ-1753767777256-93"}
{"time":"2025-07-29 13:42:59.299","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 1978ms - RequestId: REQ-1753767777256-93"}
{"time":"2025-07-29 13:42:59.299","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"227","traceId":"","appName":"AppTokenServer","message":"Cache miss - Generating new token - RequestId: REQ-1753767777256-93"}
{"time":"2025-07-29 13:42:59.299","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"253","traceId":"","appName":"AppTokenServer","message":"Protocol validation time: 0ms - RequestId: REQ-1753767777256-93"}
{"time":"2025-07-29 13:42:59.299","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"257","traceId":"","appName":"AppTokenServer","message":"Calling STS service - RequestId: REQ-1753767777256-93"}
{"time":"2025-07-29 13:42:59.300","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"169","traceId":"","appName":"AppTokenServer","message":"Generated new session name: sts-dpTJipEz-20250729134259"}
{"time":"2025-07-29 13:42:59.300","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"46","traceId":"","appName":"AppTokenServer","message":"Starting STS assume role operation - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-dpTJipEz-20250729134259 (auto-generated: true), Duration: 3600s, Protocol: HTTPS"}
{"time":"2025-07-29 13:42:59.310","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.PolicyServiceImpl","line":"54","traceId":"","appName":"AppTokenServer","message":"Enabled policy not found for name: 1"}
{"time":"2025-07-29 13:42:59.310","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.PolicyServiceImpl","line":"215","traceId":"","appName":"AppTokenServer","message":"Policy not found by name: 1"}
{"time":"2025-07-29 13:42:59.321","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:42:59.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: b0fbcd8eb56442d68b515e590dd13449 (Generated)"}
{"time":"2025-07-29 13:42:59.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:42:59.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:42:59.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:42:59.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:42:59.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  request-start-time: 1753767777128"}
{"time":"2025-07-29 13:42:59.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  content-length: 70"}
{"time":"2025-07-29 13:42:59.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:42:59.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:42:59.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:42:59.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:42:59.334","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 13:42:59.334","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 500, TraceID: b0fbcd8eb56442d68b515e590dd13449"}
{"time":"2025-07-29 13:42:59.334","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 13:42:59.334","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  X-Trace-ID: b0fbcd8eb56442d68b515e590dd13449"}
{"time":"2025-07-29 13:42:59.334","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 13:42:59.335","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 13:42:59.335","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 05:42:59 GMT"}
{"time":"2025-07-29 13:42:59.335","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"  Connection: close"}
{"time":"2025-07-29 13:42:59.335","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 13:42:59.335","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"b0fbcd8eb56442d68b515e590dd13449","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: b0fbcd8eb56442d68b515e590dd13449 ==="}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 96fc02d5d5ba461997cc3877d1080bf1 (Generated)"}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"  request-start-time: 1753767785153"}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:43:05.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"96fc02d5d5ba461997cc3877d1080bf1","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:43:05.157","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 13:43:05.157","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753767785157-93 ==="}
{"time":"2025-07-29 13:43:05.157","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 13:43:05.157","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 0ms - RequestId: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:05.183","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 25ms - RequestId: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:05.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"227","traceId":"","appName":"AppTokenServer","message":"Cache miss - Generating new token - RequestId: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:05.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"253","traceId":"","appName":"AppTokenServer","message":"Protocol validation time: 0ms - RequestId: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:05.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"257","traceId":"","appName":"AppTokenServer","message":"Calling STS service - RequestId: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:05.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"169","traceId":"","appName":"AppTokenServer","message":"Generated new session name: sts-zAVF1vLX-20250729134305"}
{"time":"2025-07-29 13:43:05.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"46","traceId":"","appName":"AppTokenServer","message":"Starting STS assume role operation - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-zAVF1vLX-20250729134305 (auto-generated: true), Duration: 3600s, Protocol: HTTPS"}
{"time":"2025-07-29 13:43:05.199","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.PolicyServiceImpl","line":"211","traceId":"","appName":"AppTokenServer","message":"Found policy by name: write_policy -> ID: 4"}
{"time":"2025-07-29 13:43:05.199","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"69","traceId":"","appName":"AppTokenServer","message":"Applied policy: policy name: write_policy"}
{"time":"2025-07-29 13:43:05.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"83","traceId":"","appName":"AppTokenServer","message":"STS assume role operation completed successfully - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-zAVF1vLX-20250729134305"}
{"time":"2025-07-29 13:43:05.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"271","traceId":"","appName":"AppTokenServer","message":"STS service call time: 461ms - RequestId: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:05.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"272","traceId":"","appName":"AppTokenServer","message":"STS token generated successfully - StatusCode: 200, AccessKeyId: STS.NZqL***, Expiration: 2025-07-29T14:43:06Z - RequestId: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:05.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"291","traceId":"","appName":"AppTokenServer","message":"Cache async setup time: 0ms - RequestId: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:05.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"300","traceId":"","appName":"AppTokenServer","message":"Response header creation time: 0ms - RequestId: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:05.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"304","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Completed Successfully - RequestId: REQ-1753767785157-93, Total Time: 488ms ==="}
{"time":"2025-07-29 13:43:05.646","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"305","traceId":"","appName":"AppTokenServer","message":"Time breakdown - Config: N/Ams, Cache Check: N/Ams, STS Call: 461ms, Total: 488ms - RequestId: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:05.646","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:43:05.647","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 7c8a5c3ec2bd4e83bc97c0d3cb208112 (Generated)"}
{"time":"2025-07-29 13:43:05.647","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:43:05.647","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:43:05.647","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:43:05.647","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:43:05.647","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  request-start-time: 1753767785153"}
{"time":"2025-07-29 13:43:05.647","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 13:43:05.647","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:43:05.648","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:43:05.648","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:43:05.648","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:43:05.649","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 13:43:05.649","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: 7c8a5c3ec2bd4e83bc97c0d3cb208112"}
{"time":"2025-07-29 13:43:05.649","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 13:43:05.649","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  X-Trace-ID: 7c8a5c3ec2bd4e83bc97c0d3cb208112"}
{"time":"2025-07-29 13:43:05.649","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  X-Cache-Status: MISS"}
{"time":"2025-07-29 13:43:05.649","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5"}
{"time":"2025-07-29 13:43:05.649","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3600"}
{"time":"2025-07-29 13:43:05.649","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  X-Response-Time: 1753767785645"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  X-STS-Call-Time: 461"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 05:43:05 GMT"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 13:43:05.651","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"7c8a5c3ec2bd4e83bc97c0d3cb208112","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 7c8a5c3ec2bd4e83bc97c0d3cb208112 ==="}
{"time":"2025-07-29 13:43:05.671","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"143","traceId":"","appName":"AppTokenServer","message":"Successfully cached STS token with key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5 for 600 seconds"}
{"time":"2025-07-29 13:43:05.671","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"285","traceId":"","appName":"AppTokenServer","message":"Async cache put time: 26ms - RequestId: REQ-1753767785157-93"}
{"time":"2025-07-29 13:43:06.839","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:43:06.840","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 407c5cfc0921450dafe4e42d329c4a71 (Generated)"}
{"time":"2025-07-29 13:43:06.840","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:43:06.840","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:43:06.840","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:43:06.840","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:43:06.840","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"  request-start-time: 1753767786837"}
{"time":"2025-07-29 13:43:06.841","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 13:43:06.841","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:43:06.841","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:43:06.841","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:43:06.841","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"407c5cfc0921450dafe4e42d329c4a71","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:43:06.841","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 13:43:06.841","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753767786841-107 ==="}
{"time":"2025-07-29 13:43:06.843","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 13:43:06.843","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 0ms - RequestId: REQ-1753767786841-107"}
{"time":"2025-07-29 13:43:06.925","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"111","traceId":"","appName":"AppTokenServer","message":"Cache hit for key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5 - Retrieved valid STS token from cache, expires at: 2025-07-29T14:43:06Z"}
{"time":"2025-07-29 13:43:06.925","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 82ms - RequestId: REQ-1753767786841-107"}
{"time":"2025-07-29 13:43:06.925","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"221","traceId":"","appName":"AppTokenServer","message":"=== Cache Hit - Request Completed - RequestId: REQ-1753767786841-107, Total Time: 84ms ==="}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 9492c2604fcd413b9e254f13b5688d17 (Generated)"}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  request-start-time: 1753767786837"}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:43:06.926","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:43:06.927","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 13:43:06.927","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: 9492c2604fcd413b9e254f13b5688d17"}
{"time":"2025-07-29 13:43:06.927","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 13:43:06.927","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  X-Trace-ID: 9492c2604fcd413b9e254f13b5688d17"}
{"time":"2025-07-29 13:43:06.927","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  X-Cache-Status: HIT"}
{"time":"2025-07-29 13:43:06.927","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5"}
{"time":"2025-07-29 13:43:06.927","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3599"}
{"time":"2025-07-29 13:43:06.927","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  X-Response-Time: 1753767786925"}
{"time":"2025-07-29 13:43:06.928","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-29 13:43:06.928","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-29 13:43:06.928","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753767786841-107"}
{"time":"2025-07-29 13:43:06.928","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 13:43:06.928","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 13:43:06.928","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 05:43:06 GMT"}
{"time":"2025-07-29 13:43:06.928","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 13:43:06.928","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 13:43:06.928","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 13:43:06.928","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 13:43:06.928","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-6","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"9492c2604fcd413b9e254f13b5688d17","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 9492c2604fcd413b9e254f13b5688d17 ==="}
{"time":"2025-07-29 13:53:29.700","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 13:53:29.908","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 13:53:29.934","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown initiated..."}
{"time":"2025-07-29 13:53:29.938","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown completed."}
{"time":"2025-07-29 13:57:52.150","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 13:57:52.180","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 37384 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 13:57:52.181","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 13:57:52.815","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 13:57:52.817","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 13:57:52.843","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 13:57:53.290","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 13:57:53.300","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 13:57:53.301","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 13:57:53.302","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 13:57:53.356","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 13:57:53.356","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1148 ms"}
{"time":"2025-07-29 13:57:53.715","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-29 13:57:53.729","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-29 13:57:53.826","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 13:57:53.835","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 13:57:53.838","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 13:57:53.840","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 13:57:54.468","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 13:57:54.503","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 13:57:54.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 13:57:54.525","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.732 seconds (process running for 3.176)"}
{"time":"2025-07-29 13:57:54.791","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Starting..."}
{"time":"2025-07-29 13:57:54.808","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 13:57:54.808","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 13:57:54.810","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 2 ms"}
{"time":"2025-07-29 13:57:59.940","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3058176e"}
{"time":"2025-07-29 13:57:59.945","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Start completed."}
{"time":"2025-07-29 13:58:33.733","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:58:33.733","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 7efafa2455894b03adcde0bd567947d1 (Generated)"}
{"time":"2025-07-29 13:58:33.733","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:58:33.733","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:58:33.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:58:33.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:58:33.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"  request-start-time: 1753768713680"}
{"time":"2025-07-29 13:58:33.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 13:58:33.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:58:33.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:58:33.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:58:33.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"7efafa2455894b03adcde0bd567947d1","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:58:33.806","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 13:58:33.807","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753768713805-93 ==="}
{"time":"2025-07-29 13:58:33.807","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 13:58:36.077","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"329","traceId":"","appName":"AppTokenServer","message":"AES decrypting Aliyun configuration"}
{"time":"2025-07-29 13:58:36.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"336","traceId":"","appName":"AppTokenServer","message":"Aliyun configuration AES decryption completed - AccessKeyID length: 24, AccessKeySecret length: 30, RoleArn length: 41"}
{"time":"2025-07-29 13:58:36.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.entity.AliyunAk","line":"148","traceId":"","appName":"AppTokenServer","message":"Successfully decrypted Aliyun AK configuration - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ"}
{"time":"2025-07-29 13:58:36.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AliyunAkServiceImpl","line":"106","traceId":"","appName":"AppTokenServer","message":"Retrieved and decrypted enabled Aliyun AK configuration by token - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ, Status: 1"}
{"time":"2025-07-29 13:58:36.100","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 2293ms - RequestId: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:36.126","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 24ms - RequestId: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:36.126","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"227","traceId":"","appName":"AppTokenServer","message":"Cache miss - Generating new token - RequestId: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:36.127","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"253","traceId":"","appName":"AppTokenServer","message":"Protocol validation time: 0ms - RequestId: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:36.127","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"257","traceId":"","appName":"AppTokenServer","message":"Calling STS service - RequestId: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:36.128","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"169","traceId":"","appName":"AppTokenServer","message":"Generated new session name: sts-XteZoKeo-20250729135836"}
{"time":"2025-07-29 13:58:36.129","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"46","traceId":"","appName":"AppTokenServer","message":"Starting STS assume role operation - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-XteZoKeo-20250729135836 (auto-generated: true), Duration: 3600s, Protocol: HTTPS"}
{"time":"2025-07-29 13:58:36.143","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.PolicyServiceImpl","line":"301","traceId":"","appName":"AppTokenServer","message":"Found policy by name: write_policy -> ID: 4"}
{"time":"2025-07-29 13:58:36.143","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"69","traceId":"","appName":"AppTokenServer","message":"Applied policy: policy name: write_policy"}
{"time":"2025-07-29 13:58:36.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"83","traceId":"","appName":"AppTokenServer","message":"STS assume role operation completed successfully - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-XteZoKeo-20250729135836"}
{"time":"2025-07-29 13:58:36.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"271","traceId":"","appName":"AppTokenServer","message":"STS service call time: 516ms - RequestId: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:36.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"272","traceId":"","appName":"AppTokenServer","message":"STS token generated successfully - StatusCode: 200, AccessKeyId: STS.NZHH***, Expiration: 2025-07-29T14:58:37Z - RequestId: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:36.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"291","traceId":"","appName":"AppTokenServer","message":"Cache async setup time: 1ms - RequestId: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:36.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"300","traceId":"","appName":"AppTokenServer","message":"Response header creation time: 0ms - RequestId: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:36.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"304","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Completed Successfully - RequestId: REQ-1753768713805-93, Total Time: 2840ms ==="}
{"time":"2025-07-29 13:58:36.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"305","traceId":"","appName":"AppTokenServer","message":"Time breakdown - Config: N/Ams, Cache Check: N/Ams, STS Call: 516ms, Total: 2840ms - RequestId: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:36.657","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:58:36.657","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 5c933d1b3ce4495083d5cfa1d125fe09 (Generated)"}
{"time":"2025-07-29 13:58:36.657","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:58:36.657","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:58:36.657","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:58:36.657","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:58:36.657","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  request-start-time: 1753768713680"}
{"time":"2025-07-29 13:58:36.657","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 13:58:36.657","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:58:36.658","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:58:36.658","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:58:36.658","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:58:36.667","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 13:58:36.668","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: 5c933d1b3ce4495083d5cfa1d125fe09"}
{"time":"2025-07-29 13:58:36.668","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 13:58:36.668","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  X-Trace-ID: 5c933d1b3ce4495083d5cfa1d125fe09"}
{"time":"2025-07-29 13:58:36.668","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  X-Cache-Status: MISS"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3600"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  X-Response-Time: 1753768716644"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  X-STS-Call-Time: 516"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 05:58:36 GMT"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 13:58:36.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"5c933d1b3ce4495083d5cfa1d125fe09","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 5c933d1b3ce4495083d5cfa1d125fe09 ==="}
{"time":"2025-07-29 13:58:36.673","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"143","traceId":"","appName":"AppTokenServer","message":"Successfully cached STS token with key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5 for 600 seconds"}
{"time":"2025-07-29 13:58:36.673","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"285","traceId":"","appName":"AppTokenServer","message":"Async cache put time: 29ms - RequestId: REQ-1753768713805-93"}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: c0a9abf1f2b14dabbe9dd81cf55dea1c (Generated)"}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"  request-start-time: 1753768728087"}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:58:48.090","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"c0a9abf1f2b14dabbe9dd81cf55dea1c","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:58:48.091","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 13:58:48.091","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753768728091-107 ==="}
{"time":"2025-07-29 13:58:48.091","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 13:58:48.091","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 0ms - RequestId: REQ-1753768728091-107"}
{"time":"2025-07-29 13:58:48.121","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"111","traceId":"","appName":"AppTokenServer","message":"Cache hit for key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5 - Retrieved valid STS token from cache, expires at: 2025-07-29T14:58:37Z"}
{"time":"2025-07-29 13:58:48.121","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 30ms - RequestId: REQ-1753768728091-107"}
{"time":"2025-07-29 13:58:48.121","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"221","traceId":"","appName":"AppTokenServer","message":"=== Cache Hit - Request Completed - RequestId: REQ-1753768728091-107, Total Time: 30ms ==="}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: f549fe78d39940789ab8fa454c8cf0fa (Generated)"}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  request-start-time: 1753768728087"}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:58:48.122","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: f549fe78d39940789ab8fa454c8cf0fa"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  X-Trace-ID: f549fe78d39940789ab8fa454c8cf0fa"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  X-Cache-Status: HIT"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3588"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  X-Response-Time: 1753768728121"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753768728091-107"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 05:58:48 GMT"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 13:58:48.123","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 13:58:48.124","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 13:58:48.124","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"f549fe78d39940789ab8fa454c8cf0fa","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: f549fe78d39940789ab8fa454c8cf0fa ==="}
{"time":"2025-07-29 13:59:15.075","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:59:15.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 2689cbfb6f3b45f3bfd11efaca9cfcd5 (Generated)"}
{"time":"2025-07-29 13:59:15.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:59:15.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:59:15.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:59:15.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:59:15.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"  request-start-time: 1753768755072"}
{"time":"2025-07-29 13:59:15.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 13:59:15.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:59:15.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:59:15.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:59:15.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-7","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"2689cbfb6f3b45f3bfd11efaca9cfcd5","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:59:15.077","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 13:59:15.077","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753768755077-107 ==="}
{"time":"2025-07-29 13:59:15.077","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 13:59:15.077","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 0ms - RequestId: REQ-1753768755077-107"}
{"time":"2025-07-29 13:59:15.095","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"111","traceId":"","appName":"AppTokenServer","message":"Cache hit for key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5 - Retrieved valid STS token from cache, expires at: 2025-07-29T14:58:37Z"}
{"time":"2025-07-29 13:59:15.095","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 18ms - RequestId: REQ-1753768755077-107"}
{"time":"2025-07-29 13:59:15.095","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"221","traceId":"","appName":"AppTokenServer","message":"=== Cache Hit - Request Completed - RequestId: REQ-1753768755077-107, Total Time: 18ms ==="}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 4075793e8cc84aa1b7ffd83ab2d0b188 (Generated)"}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  request-start-time: 1753768755072"}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 13:59:15.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: 4075793e8cc84aa1b7ffd83ab2d0b188"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  X-Trace-ID: 4075793e8cc84aa1b7ffd83ab2d0b188"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  X-Cache-Status: HIT"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3561"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  X-Response-Time: 1753768755095"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753768755077-107"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 05:59:15 GMT"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 13:59:15.097","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-10","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"4075793e8cc84aa1b7ffd83ab2d0b188","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 4075793e8cc84aa1b7ffd83ab2d0b188 ==="}
{"time":"2025-07-29 14:05:48.504","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 14:05:48.728","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 14:05:48.747","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown initiated..."}
{"time":"2025-07-29 14:05:48.750","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown completed."}
{"time":"2025-07-29 14:05:51.806","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 14:05:51.837","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 66444 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 14:05:51.839","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 14:05:52.447","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 14:05:52.449","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 14:05:52.471","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 14:05:52.974","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 14:05:52.989","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 14:05:52.991","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 14:05:52.991","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 14:05:53.054","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 14:05:53.055","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1188 ms"}
{"time":"2025-07-29 14:05:53.413","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-29 14:05:53.426","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-29 14:05:53.528","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 14:05:53.532","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 14:05:53.534","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 14:05:53.536","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 14:05:53.538","ip":"DESKTOP-G92L71C","level":"WARN","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","line":"635","traceId":"","appName":"AppTokenServer","message":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'stsClient' defined in class path resource [com/imile/stsserver/config/StsConfig.class]: Failed to instantiate [com.aliyun.sts20150401.Client]: Factory method 'stsClient' threw exception with message: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)"}
{"time":"2025-07-29 14:05:53.546","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Stopping service [Tomcat]"}
{"time":"2025-07-29 14:05:53.557","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLogger","line":"82","traceId":"","appName":"AppTokenServer","message":"

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled."}
{"time":"2025-07-29 14:19:01.295","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 14:19:01.326","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 59356 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 14:19:01.326","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 14:19:01.936","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 14:19:01.937","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 14:19:01.959","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 14:19:02.401","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 14:19:02.409","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 14:19:02.410","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 14:19:02.410","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 14:19:02.457","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 14:19:02.457","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1104 ms"}
{"time":"2025-07-29 14:19:02.818","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-29 14:19:02.835","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-29 14:19:02.929","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 14:19:02.933","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 14:19:02.935","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 14:19:02.938","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 14:19:02.943","ip":"DESKTOP-G92L71C","level":"WARN","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","line":"635","traceId":"","appName":"AppTokenServer","message":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'stsClient' defined in class path resource [com/imile/stsserver/config/StsConfig.class]: Failed to instantiate [com.aliyun.sts20150401.Client]: Factory method 'stsClient' threw exception with message: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)"}
{"time":"2025-07-29 14:19:02.960","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Stopping service [Tomcat]"}
{"time":"2025-07-29 14:19:02.974","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLogger","line":"82","traceId":"","appName":"AppTokenServer","message":"

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled."}
{"time":"2025-07-29 15:05:06.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 15:05:06.128","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 71072 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 15:05:06.128","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 15:05:06.743","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 15:05:06.744","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 15:05:06.767","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 15:05:07.184","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 15:05:07.192","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 15:05:07.193","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 15:05:07.193","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 15:05:07.236","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 15:05:07.237","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1082 ms"}
{"time":"2025-07-29 15:05:07.592","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-29 15:05:07.605","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-29 15:05:07.708","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 15:05:07.712","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 15:05:07.713","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 15:05:07.715","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 15:05:07.716","ip":"DESKTOP-G92L71C","level":"WARN","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","line":"635","traceId":"","appName":"AppTokenServer","message":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'stsClient' defined in class path resource [com/imile/stsserver/config/StsConfig.class]: Failed to instantiate [com.aliyun.sts20150401.Client]: Factory method 'stsClient' threw exception with message: Aliyun Access Key ID and Secret must be provided either through application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)"}
{"time":"2025-07-29 15:05:07.726","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Stopping service [Tomcat]"}
{"time":"2025-07-29 15:05:07.736","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLogger","line":"82","traceId":"","appName":"AppTokenServer","message":"

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled."}
{"time":"2025-07-29 15:05:46.857","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 15:05:46.888","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 19488 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 15:05:46.889","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 15:05:47.525","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 15:05:47.526","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 15:05:47.548","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 15:05:47.984","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 15:05:47.994","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 15:05:47.995","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 15:05:47.996","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 15:05:48.048","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 15:05:48.049","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1133 ms"}
{"time":"2025-07-29 15:05:48.397","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-29 15:05:48.410","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-29 15:05:48.509","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 15:05:48.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 15:05:48.515","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 15:05:48.517","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 15:05:48.980","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 15:05:49.018","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 15:05:49.030","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 15:05:49.041","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.51 seconds (process running for 2.881)"}
{"time":"2025-07-29 15:05:49.382","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Starting..."}
{"time":"2025-07-29 15:05:49.415","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 15:05:49.415","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 15:05:49.418","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 3 ms"}
{"time":"2025-07-29 15:05:54.165","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@783c6d02"}
{"time":"2025-07-29 15:05:54.173","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Start completed."}
{"time":"2025-07-29 15:06:43.639","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 15:06:43.834","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 15:06:43.849","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown initiated..."}
{"time":"2025-07-29 15:06:43.854","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown completed."}
{"time":"2025-07-29 15:07:36.542","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 15:07:36.574","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 64932 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 15:07:36.575","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 15:07:37.154","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 15:07:37.155","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 15:07:37.178","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 15:07:37.622","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 80 (http)"}
{"time":"2025-07-29 15:07:37.632","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-80\"]"}
{"time":"2025-07-29 15:07:37.633","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 15:07:37.633","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 15:07:37.684","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 15:07:37.684","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1085 ms"}
{"time":"2025-07-29 15:07:38.041","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-29 15:07:38.055","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-29 15:07:38.152","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 15:07:38.156","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 15:07:38.158","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 15:07:38.159","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 15:07:38.614","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 15:07:38.652","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-80\"]"}
{"time":"2025-07-29 15:07:38.664","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 80 (http) with context path '/'"}
{"time":"2025-07-29 15:07:38.673","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.44 seconds (process running for 2.769)"}
{"time":"2025-07-29 15:07:39.096","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Starting..."}
{"time":"2025-07-29 15:07:39.127","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 15:07:39.128","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 15:07:39.130","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 2 ms"}
{"time":"2025-07-29 15:07:39.387","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1fb9a765"}
{"time":"2025-07-29 15:07:39.394","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Start completed."}
{"time":"2025-07-29 15:07:43.883","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 15:07:43.883","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 8552a5ae68c2492787ccb81e974922cb (Generated)"}
{"time":"2025-07-29 15:07:43.883","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 15:07:43.884","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 15:07:43.884","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 15:07:43.884","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 15:07:43.884","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"  request-start-time: 1753772863818"}
{"time":"2025-07-29 15:07:43.884","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 15:07:43.884","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 15:07:43.884","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-29 15:07:43.884","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 15:07:43.884","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"8552a5ae68c2492787ccb81e974922cb","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 15:07:43.989","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 15:07:43.989","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753772863987-93 ==="}
{"time":"2025-07-29 15:07:43.989","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 15:07:45.704","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"329","traceId":"","appName":"AppTokenServer","message":"AES decrypting Aliyun configuration"}
{"time":"2025-07-29 15:07:45.714","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"336","traceId":"","appName":"AppTokenServer","message":"Aliyun configuration AES decryption completed - AccessKeyID length: 24, AccessKeySecret length: 30, RoleArn length: 41"}
{"time":"2025-07-29 15:07:45.714","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.entity.AliyunAk","line":"148","traceId":"","appName":"AppTokenServer","message":"Successfully decrypted Aliyun AK configuration - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ"}
{"time":"2025-07-29 15:07:45.714","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AliyunAkServiceImpl","line":"106","traceId":"","appName":"AppTokenServer","message":"Retrieved and decrypted enabled Aliyun AK configuration by token - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ, Status: 1"}
{"time":"2025-07-29 15:07:45.723","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 1734ms - RequestId: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:45.823","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 100ms - RequestId: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:45.823","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"227","traceId":"","appName":"AppTokenServer","message":"Cache miss - Generating new token - RequestId: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:45.825","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"253","traceId":"","appName":"AppTokenServer","message":"Protocol validation time: 2ms - RequestId: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:45.825","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"257","traceId":"","appName":"AppTokenServer","message":"Calling STS service - RequestId: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:45.825","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"169","traceId":"","appName":"AppTokenServer","message":"Generated new session name: sts-5GlVgISg-20250729150745"}
{"time":"2025-07-29 15:07:45.825","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"46","traceId":"","appName":"AppTokenServer","message":"Starting STS assume role operation - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-5GlVgISg-20250729150745 (auto-generated: true), Duration: 3600s, Protocol: HTTPS"}
{"time":"2025-07-29 15:07:45.854","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.PolicyServiceImpl","line":"301","traceId":"","appName":"AppTokenServer","message":"Found policy by name: write_policy -> ID: 4"}
{"time":"2025-07-29 15:07:45.855","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"69","traceId":"","appName":"AppTokenServer","message":"Applied policy: policy name: write_policy"}
{"time":"2025-07-29 15:07:46.302","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"83","traceId":"","appName":"AppTokenServer","message":"STS assume role operation completed successfully - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-5GlVgISg-20250729150745"}
{"time":"2025-07-29 15:07:46.302","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"271","traceId":"","appName":"AppTokenServer","message":"STS service call time: 477ms - RequestId: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:46.302","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"272","traceId":"","appName":"AppTokenServer","message":"STS token generated successfully - StatusCode: 200, AccessKeyId: STS.NXrf***, Expiration: 2025-07-29T16:07:46Z - RequestId: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:46.303","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"291","traceId":"","appName":"AppTokenServer","message":"Cache async setup time: 1ms - RequestId: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:46.304","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"300","traceId":"","appName":"AppTokenServer","message":"Response header creation time: 0ms - RequestId: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:46.304","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"304","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Completed Successfully - RequestId: REQ-1753772863987-93, Total Time: 2317ms ==="}
{"time":"2025-07-29 15:07:46.304","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"305","traceId":"","appName":"AppTokenServer","message":"Time breakdown - Config: N/Ams, Cache Check: N/Ams, STS Call: 477ms, Total: 2317ms - RequestId: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: bf774ee409dd49daaa85432d7a26a73d (Generated)"}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  request-start-time: 1753772863818"}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 15:07:46.316","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: bf774ee409dd49daaa85432d7a26a73d"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  X-Trace-ID: bf774ee409dd49daaa85432d7a26a73d"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  X-Cache-Status: MISS"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3599"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  X-Response-Time: 1753772866303"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  X-STS-Call-Time: 477"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 07:07:46 GMT"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 15:07:46.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"bf774ee409dd49daaa85432d7a26a73d","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: bf774ee409dd49daaa85432d7a26a73d ==="}
{"time":"2025-07-29 15:07:46.355","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"143","traceId":"","appName":"AppTokenServer","message":"Successfully cached STS token with key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5 for 600 seconds"}
{"time":"2025-07-29 15:07:46.355","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"285","traceId":"","appName":"AppTokenServer","message":"Async cache put time: 52ms - RequestId: REQ-1753772863987-93"}
{"time":"2025-07-29 15:07:50.923","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 15:07:50.923","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 2e364b667f854783b850408b35e80db1 (Generated)"}
{"time":"2025-07-29 15:07:50.923","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 15:07:50.923","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 15:07:50.924","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 15:07:50.924","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 15:07:50.924","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"  request-start-time: 1753772870920"}
{"time":"2025-07-29 15:07:50.924","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 15:07:50.924","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 15:07:50.924","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-29 15:07:50.924","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 15:07:50.924","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"2e364b667f854783b850408b35e80db1","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 15:07:50.925","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-29 15:07:50.925","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753772870925-107 ==="}
{"time":"2025-07-29 15:07:50.925","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-29 15:07:50.925","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 0ms - RequestId: REQ-1753772870925-107"}
{"time":"2025-07-29 15:07:50.946","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"111","traceId":"","appName":"AppTokenServer","message":"Cache hit for key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5 - Retrieved valid STS token from cache, expires at: 2025-07-29T16:07:46Z"}
{"time":"2025-07-29 15:07:50.946","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 20ms - RequestId: REQ-1753772870925-107"}
{"time":"2025-07-29 15:07:50.946","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"221","traceId":"","appName":"AppTokenServer","message":"=== Cache Hit - Request Completed - RequestId: REQ-1753772870925-107, Total Time: 21ms ==="}
{"time":"2025-07-29 15:07:50.948","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 15:07:50.948","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: ff6801bd11554647a93485b4326649d9 (Generated)"}
{"time":"2025-07-29 15:07:50.948","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 15:07:50.948","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 15:07:50.948","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-29 15:07:50.948","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 15:07:50.948","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  request-start-time: 1753772870920"}
{"time":"2025-07-29 15:07:50.948","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-29 15:07:50.949","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 15:07:50.949","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-29 15:07:50.949","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 15:07:50.949","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: ff6801bd11554647a93485b4326649d9"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  X-Trace-ID: ff6801bd11554647a93485b4326649d9"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  X-Cache-Status: HIT"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3595"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  X-Response-Time: 1753772870946"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753772870925-107"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 07:07:50 GMT"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 15:07:50.950","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"ff6801bd11554647a93485b4326649d9","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: ff6801bd11554647a93485b4326649d9 ==="}
{"time":"2025-07-29 15:07:53.702","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 15:07:53.973","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 15:07:54.000","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown initiated..."}
{"time":"2025-07-29 15:07:54.006","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown completed."}
{"time":"2025-07-29 16:11:09.131","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 16:11:09.165","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 69164 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 16:11:09.166","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 16:11:09.814","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 16:11:09.817","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 16:11:09.841","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 16:11:10.375","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 16:11:10.386","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 16:11:10.387","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 16:11:10.387","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 16:11:10.434","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 16:11:10.434","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1233 ms"}
{"time":"2025-07-29 16:11:10.810","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-29 16:11:10.826","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-29 16:11:10.933","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 16:11:10.936","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 16:11:10.938","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 16:11:10.940","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 16:11:11.388","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 16:11:11.419","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 16:11:11.433","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 16:11:11.447","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.668 seconds (process running for 3.161)"}
{"time":"2025-07-29 16:11:11.626","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 16:11:11.627","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 16:11:11.628","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 1 ms"}
{"time":"2025-07-29 16:11:11.635","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Starting..."}
{"time":"2025-07-29 16:11:16.494","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@139cb96"}
{"time":"2025-07-29 16:11:16.499","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Start completed."}
{"time":"2025-07-29 16:11:43.054","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 16:11:43.054","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"Request Details - Method: GET, URI: /api/monitor/metrics, TraceID: 6f3bf047f53f4f2fbc3f8a111309a72b (Generated)"}
{"time":"2025-07-29 16:11:43.054","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 16:11:43.054","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 16:11:43.054","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:11:43.055","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  request-start-time: 1753776702994"}
{"time":"2025-07-29 16:11:43.055","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 16:11:43.055","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 16:11:43.055","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 16:11:43.055","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:11:46.372","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 16:11:46.373","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"Response Details - Method: GET, URI: /api/monitor/metrics, Status: 200, TraceID: 6f3bf047f53f4f2fbc3f8a111309a72b"}
{"time":"2025-07-29 16:11:46.373","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 16:11:46.373","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  X-Trace-ID: 6f3bf047f53f4f2fbc3f8a111309a72b"}
{"time":"2025-07-29 16:11:46.373","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 16:11:46.373","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 16:11:46.374","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 08:11:46 GMT"}
{"time":"2025-07-29 16:11:46.374","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 16:11:46.374","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 16:11:46.374","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 16:11:46.374","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 16:11:46.374","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"6f3bf047f53f4f2fbc3f8a111309a72b","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 6f3bf047f53f4f2fbc3f8a111309a72b ==="}
{"time":"2025-07-29 16:13:37.902","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 16:13:37.902","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"Request Details - Method: GET, URI: /api/monitor/sts-metrics, TraceID: 82578813ca65445093a0b5aafa256465 (Generated)"}
{"time":"2025-07-29 16:13:37.903","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 16:13:37.903","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 16:13:37.903","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:13:37.903","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  request-start-time: 1753776817899"}
{"time":"2025-07-29 16:13:37.903","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 16:13:37.903","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 16:13:37.904","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 16:13:37.904","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:13:38.010","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 16:13:38.010","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"Response Details - Method: GET, URI: /api/monitor/sts-metrics, Status: 200, TraceID: 82578813ca65445093a0b5aafa256465"}
{"time":"2025-07-29 16:13:38.011","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 16:13:38.011","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  X-Trace-ID: 82578813ca65445093a0b5aafa256465"}
{"time":"2025-07-29 16:13:38.011","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 16:13:38.011","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 16:13:38.013","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 08:13:38 GMT"}
{"time":"2025-07-29 16:13:38.013","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 16:13:38.013","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 16:13:38.013","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 16:13:38.013","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 16:13:38.014","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-4","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"82578813ca65445093a0b5aafa256465","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 82578813ca65445093a0b5aafa256465 ==="}
{"time":"2025-07-29 16:14:15.851","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 16:14:15.852","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"Request Details - Method: GET, URI: /api/monitor/health, TraceID: 917aac9c59c14185bd7e7138dc9285c6 (Generated)"}
{"time":"2025-07-29 16:14:15.852","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 16:14:15.852","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 16:14:15.852","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:14:15.852","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  request-start-time: 1753776855847"}
{"time":"2025-07-29 16:14:15.853","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 16:14:15.853","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 16:14:15.853","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 16:14:15.853","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:14:16.018","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 16:14:16.019","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"Response Details - Method: GET, URI: /api/monitor/health, Status: 200, TraceID: 917aac9c59c14185bd7e7138dc9285c6"}
{"time":"2025-07-29 16:14:16.019","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 16:14:16.019","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  X-Trace-ID: 917aac9c59c14185bd7e7138dc9285c6"}
{"time":"2025-07-29 16:14:16.019","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 16:14:16.020","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 16:14:16.021","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 08:14:16 GMT"}
{"time":"2025-07-29 16:14:16.029","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 16:14:16.031","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 16:14:16.031","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 16:14:16.031","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 16:14:16.031","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"917aac9c59c14185bd7e7138dc9285c6","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 917aac9c59c14185bd7e7138dc9285c6 ==="}
{"time":"2025-07-29 16:14:39.228","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 16:14:39.229","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"Request Details - Method: GET, URI: /, TraceID: 189f135e7de2470cbb1e5a0b94e26407 (Generated)"}
{"time":"2025-07-29 16:14:39.229","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 16:14:39.229","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  host: **************:8080"}
{"time":"2025-07-29 16:14:39.229","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"time":"2025-07-29 16:14:39.230","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"}
{"time":"2025-07-29 16:14:39.231","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  accept-encoding: gzip, deflate"}
{"time":"2025-07-29 16:14:39.232","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  accept-language: en,zh;q=0.9,zh-CN;q=0.8,da;q=0.7,zh-TW;q=0.6"}
{"time":"2025-07-29 16:14:39.232","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  upgrade-insecure-requests: 1"}
{"time":"2025-07-29 16:14:39.232","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"Client Info - RemoteAddr: *************, UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"time":"2025-07-29 16:14:39.252","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 16:14:39.253","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"Response Details - Method: GET, URI: /, Status: 500, TraceID: 189f135e7de2470cbb1e5a0b94e26407"}
{"time":"2025-07-29 16:14:39.253","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 16:14:39.253","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  Vary: Origin"}
{"time":"2025-07-29 16:14:39.254","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  Vary: Origin"}
{"time":"2025-07-29 16:14:39.254","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  Vary: Origin"}
{"time":"2025-07-29 16:14:39.254","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  X-Trace-ID: 189f135e7de2470cbb1e5a0b94e26407"}
{"time":"2025-07-29 16:14:39.255","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 16:14:39.255","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 16:14:39.255","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 08:14:39 GMT"}
{"time":"2025-07-29 16:14:39.255","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"  Connection: close"}
{"time":"2025-07-29 16:14:39.257","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 16:14:39.257","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-8080-exec-9","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"189f135e7de2470cbb1e5a0b94e26407","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 189f135e7de2470cbb1e5a0b94e26407 ==="}
{"time":"2025-07-29 16:20:34.170","ip":"DESKTOP-G92L71C","level":"WARN","thread":"RMI TCP Accept-0","stack_trace":"java.io.IOException: The server sockets created using the LocalRMIServerSocketFactory only accept connections from clients running on the host where the RMI remote objects have been exported.
	at jdk.management.agent/sun.management.jmxremote.LocalRMIServerSocketFactory$1.accept(LocalRMIServerSocketFactory.java:114)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$AcceptLoop.executeAcceptLoop(TCPTransport.java:413)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$AcceptLoop.run(TCPTransport.java:377)
	at java.base/java.lang.Thread.run(Thread.java:840)
","class":"sun.rmi.transport.tcp","line":"236","traceId":"","appName":"AppTokenServer","message":"RMI TCP Accept-0: accept loop for ServerSocket[addr=0.0.0.0/0.0.0.0,localport=53637] throws"}
{"time":"2025-07-29 16:22:21.594","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 16:22:21.785","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 16:22:21.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown initiated..."}
{"time":"2025-07-29 16:22:21.810","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown completed."}
{"time":"2025-07-29 16:22:25.012","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 16:22:25.042","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 75304 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 16:22:25.042","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 16:22:25.628","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 16:22:25.630","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 16:22:25.650","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 16:22:26.063","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 16:22:26.074","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 16:22:26.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 16:22:26.076","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 16:22:26.129","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 16:22:26.129","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1061 ms"}
{"time":"2025-07-29 16:22:26.443","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-29 16:22:26.455","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-29 16:22:26.575","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 16:22:26.578","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 16:22:26.580","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 16:22:26.582","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 16:22:26.997","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 16:22:27.036","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 16:22:27.048","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 16:22:27.059","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.374 seconds (process running for 2.758)"}
{"time":"2025-07-29 16:22:27.497","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Starting..."}
{"time":"2025-07-29 16:22:27.512","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 16:22:27.512","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 16:22:27.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 2 ms"}
{"time":"2025-07-29 16:22:32.261","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@5486f4f7"}
{"time":"2025-07-29 16:22:32.274","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Start completed."}
{"time":"2025-07-29 16:25:08.839","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 16:25:09.037","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 16:25:09.053","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown initiated..."}
{"time":"2025-07-29 16:25:09.057","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown completed."}
{"time":"2025-07-29 16:46:32.883","ip":"**************","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 16:46:32.913","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 75996 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 16:46:32.913","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 16:46:33.515","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 16:46:33.516","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 16:46:33.539","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 16:46:33.952","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 16:46:33.962","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 16:46:33.963","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 16:46:33.963","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 16:46:34.010","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 16:46:34.011","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1070 ms"}
{"time":"2025-07-29 16:46:34.356","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-29 16:46:34.370","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-29 16:46:34.465","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 16:46:34.470","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 16:46:34.472","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 16:46:34.473","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 16:46:34.911","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 16:46:34.945","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 16:46:34.955","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 16:46:34.966","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.51 seconds (process running for 2.926)"}
{"time":"2025-07-29 16:46:34.970","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.IpAddressConfig","line":"18","traceId":"","appName":"AppTokenServer","message":"=== IP Address Configuration Started ==="}
{"time":"2025-07-29 16:46:34.971","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.IpAddressConfig","line":"31","traceId":"","appName":"AppTokenServer","message":"Local IP Address: **************"}
{"time":"2025-07-29 16:46:34.971","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.IpAddressConfig","line":"32","traceId":"","appName":"AppTokenServer","message":"Host Name: DESKTOP-G92L71C"}
{"time":"2025-07-29 16:46:34.971","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.IpAddressConfig","line":"33","traceId":"","appName":"AppTokenServer","message":"Host Info: **************(DESKTOP-G92L71C)"}
{"time":"2025-07-29 16:46:35.181","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.IpAddressConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"=== IP Address Configuration Completed ==="}
{"time":"2025-07-29 16:46:35.726","ip":"**************","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 16:46:35.726","ip":"**************","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 16:46:35.728","ip":"**************","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 2 ms"}
{"time":"2025-07-29 16:46:35.728","ip":"**************","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Starting..."}
{"time":"2025-07-29 16:46:40.681","ip":"**************","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@79fc8502"}
{"time":"2025-07-29 16:46:40.686","ip":"**************","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Start completed."}
{"time":"2025-07-29 16:47:06.092","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 16:47:06.093","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"Request Details - Method: GET, URI: /api/system/info, TraceID: 11a7a6ce069648c1b102343834ca5e61 (Generated)"}
{"time":"2025-07-29 16:47:06.093","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 16:47:06.094","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 16:47:06.094","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:47:06.096","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  request-start-time: 1753778825983"}
{"time":"2025-07-29 16:47:06.096","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 16:47:06.096","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 16:47:06.096","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 16:47:06.097","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:47:06.113","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.controller.SystemInfoController","line":"26","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"=== System Info Request Started ==="}
{"time":"2025-07-29 16:47:06.114","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.controller.SystemInfoController","line":"56","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"System info retrieved successfully - IP: **************, Host: DESKTOP-G92L71C"}
{"time":"2025-07-29 16:47:06.114","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.controller.SystemInfoController","line":"57","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"=== System Info Request Completed ==="}
{"time":"2025-07-29 16:47:06.150","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 16:47:06.151","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"Response Details - Method: GET, URI: /api/system/info, Status: 200, TraceID: 11a7a6ce069648c1b102343834ca5e61"}
{"time":"2025-07-29 16:47:06.152","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 16:47:06.153","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  X-Trace-ID: 11a7a6ce069648c1b102343834ca5e61"}
{"time":"2025-07-29 16:47:06.158","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 16:47:06.166","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 16:47:06.170","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 08:47:06 GMT"}
{"time":"2025-07-29 16:47:06.174","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 16:47:06.176","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 16:47:06.176","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 16:47:06.177","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 16:47:06.177","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"11a7a6ce069648c1b102343834ca5e61","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 11a7a6ce069648c1b102343834ca5e61 ==="}
{"time":"2025-07-29 16:47:13.352","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 16:47:13.352","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"Request Details - Method: GET, URI: /api/system/ip, TraceID: b2bc6a38d18348d089ea275e81211e8b (Generated)"}
{"time":"2025-07-29 16:47:13.352","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 16:47:13.353","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 16:47:13.353","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:47:13.353","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  request-start-time: 1753778833349"}
{"time":"2025-07-29 16:47:13.353","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 16:47:13.353","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 16:47:13.354","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 16:47:13.354","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:47:13.354","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.controller.SystemInfoController","line":"73","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"=== IP Info Request Started ==="}
{"time":"2025-07-29 16:47:13.355","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.controller.SystemInfoController","line":"93","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"IP info retrieved successfully - IP: **************, Host: DESKTOP-G92L71C"}
{"time":"2025-07-29 16:47:13.355","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.controller.SystemInfoController","line":"94","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"=== IP Info Request Completed ==="}
{"time":"2025-07-29 16:47:13.356","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 16:47:13.357","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"Response Details - Method: GET, URI: /api/system/ip, Status: 200, TraceID: b2bc6a38d18348d089ea275e81211e8b"}
{"time":"2025-07-29 16:47:13.357","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 16:47:13.358","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  X-Trace-ID: b2bc6a38d18348d089ea275e81211e8b"}
{"time":"2025-07-29 16:47:13.358","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 16:47:13.358","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 16:47:13.358","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 08:47:13 GMT"}
{"time":"2025-07-29 16:47:13.358","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 16:47:13.358","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 16:47:13.359","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 16:47:13.359","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 16:47:13.359","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"b2bc6a38d18348d089ea275e81211e8b","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: b2bc6a38d18348d089ea275e81211e8b ==="}
{"time":"2025-07-29 16:47:21.674","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-29 16:47:21.674","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"Request Details - Method: GET, URI: /api/system/test-log, TraceID: f05eef845eb54975883587f3aa0d3ae9 (Generated)"}
{"time":"2025-07-29 16:47:21.674","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-29 16:47:21.675","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-29 16:47:21.675","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:47:21.675","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  request-start-time: 1753778841671"}
{"time":"2025-07-29 16:47:21.675","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-29 16:47:21.675","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  host: 127.0.0.1:8080"}
{"time":"2025-07-29 16:47:21.675","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-29 16:47:21.675","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-29 16:47:21.676","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.controller.SystemInfoController","line":"110","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"=== Test Log Request Started ==="}
{"time":"2025-07-29 16:47:21.676","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.controller.SystemInfoController","line":"119","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"INFO level log test - Local IP: **************"}
{"time":"2025-07-29 16:47:21.676","ip":"**************","level":"WARN","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.controller.SystemInfoController","line":"120","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"WARN level log test - Local IP: **************"}
{"time":"2025-07-29 16:47:21.676","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.controller.SystemInfoController","line":"127","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"=== Test Log Request Completed ==="}
{"time":"2025-07-29 16:47:21.678","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-29 16:47:21.678","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"Response Details - Method: GET, URI: /api/system/test-log, Status: 200, TraceID: f05eef845eb54975883587f3aa0d3ae9"}
{"time":"2025-07-29 16:47:21.678","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-29 16:47:21.678","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  X-Trace-ID: f05eef845eb54975883587f3aa0d3ae9"}
{"time":"2025-07-29 16:47:21.678","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-29 16:47:21.678","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-29 16:47:21.679","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  Date: Tue, 29 Jul 2025 08:47:21 GMT"}
{"time":"2025-07-29 16:47:21.679","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-29 16:47:21.679","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-29 16:47:21.679","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-29 16:47:21.679","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-29 16:47:21.679","ip":"**************","level":"INFO","thread":"http-nio-8080-exec-5","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"f05eef845eb54975883587f3aa0d3ae9","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: f05eef845eb54975883587f3aa0d3ae9 ==="}
{"time":"2025-07-29 16:48:04.068","ip":"**************","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 16:48:04.265","ip":"**************","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 16:48:04.282","ip":"**************","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown initiated..."}
{"time":"2025-07-29 16:48:04.286","ip":"**************","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown completed."}
{"time":"2025-07-29 16:48:06.767","ip":"**************","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-29 16:48:06.819","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 46220 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-29 16:48:06.820","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-29 16:48:07.636","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-29 16:48:07.638","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-29 16:48:07.662","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-29 16:48:08.172","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 8080 (http)"}
{"time":"2025-07-29 16:48:08.184","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 16:48:08.186","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-29 16:48:08.186","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-29 16:48:08.255","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-29 16:48:08.256","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1372 ms"}
{"time":"2025-07-29 16:48:08.662","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-29 16:48:08.677","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-29 16:48:08.783","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-29 16:48:08.786","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-29 16:48:08.789","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-29 16:48:08.790","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-29 16:48:09.249","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-29 16:48:09.292","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-8080\"]"}
{"time":"2025-07-29 16:48:09.305","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 8080 (http) with context path '/'"}
{"time":"2025-07-29 16:48:09.314","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 3.11 seconds (process running for 3.926)"}
{"time":"2025-07-29 16:48:09.320","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.IpAddressConfig","line":"18","traceId":"","appName":"AppTokenServer","message":"=== IP Address Configuration Started ==="}
{"time":"2025-07-29 16:48:09.320","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.IpAddressConfig","line":"31","traceId":"","appName":"AppTokenServer","message":"Local IP Address: **************"}
{"time":"2025-07-29 16:48:09.320","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.IpAddressConfig","line":"32","traceId":"","appName":"AppTokenServer","message":"Host Name: DESKTOP-G92L71C"}
{"time":"2025-07-29 16:48:09.320","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.IpAddressConfig","line":"33","traceId":"","appName":"AppTokenServer","message":"Host Info: **************(DESKTOP-G92L71C)"}
{"time":"2025-07-29 16:48:09.509","ip":"**************","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.IpAddressConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"=== IP Address Configuration Completed ==="}
{"time":"2025-07-29 16:48:09.948","ip":"**************","level":"INFO","thread":"RMI TCP Connection(5)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-29 16:48:09.948","ip":"**************","level":"INFO","thread":"RMI TCP Connection(5)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-29 16:48:09.949","ip":"**************","level":"INFO","thread":"RMI TCP Connection(5)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 1 ms"}
{"time":"2025-07-29 16:48:09.964","ip":"**************","level":"INFO","thread":"RMI TCP Connection(7)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Starting..."}
{"time":"2025-07-29 16:48:14.978","ip":"**************","level":"INFO","thread":"RMI TCP Connection(7)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@19db7c1"}
{"time":"2025-07-29 16:48:15.012","ip":"**************","level":"INFO","thread":"RMI TCP Connection(7)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Start completed."}
{"time":"2025-07-29 18:10:03.019","ip":"**************","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-29 18:10:03.214","ip":"**************","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-29 18:10:03.225","ip":"**************","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown initiated..."}
{"time":"2025-07-29 18:10:03.227","ip":"**************","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"HikariPool-1 - Shutdown completed."}
