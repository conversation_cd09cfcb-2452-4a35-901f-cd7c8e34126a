{"time":"2025-07-30 14:41:44.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-30 14:41:44.670","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 72880 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-30 14:41:44.670","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-30 14:41:45.379","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-30 14:41:45.382","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-30 14:41:45.406","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-30 14:41:45.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 80 (http)"}
{"time":"2025-07-30 14:41:45.875","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-80\"]"}
{"time":"2025-07-30 14:41:45.876","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-30 14:41:45.876","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-30 14:41:45.960","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-30 14:41:45.960","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1255 ms"}
{"time":"2025-07-30 14:41:46.367","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-30 14:41:46.388","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-30 14:41:46.507","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-30 14:41:46.510","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-30 14:41:46.512","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-30 14:41:46.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-30 14:41:46.989","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-30 14:41:47.023","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-80\"]"}
{"time":"2025-07-30 14:41:47.037","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 80 (http) with context path '/'"}
{"time":"2025-07-30 14:41:47.046","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.843 seconds (process running for 3.366)"}
{"time":"2025-07-30 14:41:47.303","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Starting..."}
{"time":"2025-07-30 14:41:47.321","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-30 14:41:47.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-30 14:41:47.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 0 ms"}
{"time":"2025-07-30 14:41:47.986","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@4e13d8eb"}
{"time":"2025-07-30 14:41:47.989","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Start completed."}
{"time":"2025-07-30 14:42:23.804","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 8ae1b621ef66479bbc5fa21d1cb2a5c7 (Generated)"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  request-start-time: 1753857743739"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:42:23.891","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-30 14:42:23.892","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753857743890-94 ==="}
{"time":"2025-07-30 14:42:23.892","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-30 14:42:25.952","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"329","traceId":"","appName":"AppTokenServer","message":"AES decrypting Aliyun configuration"}
{"time":"2025-07-30 14:42:25.972","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"336","traceId":"","appName":"AppTokenServer","message":"Aliyun configuration AES decryption completed - AccessKeyID length: 24, AccessKeySecret length: 30, RoleArn length: 41"}
{"time":"2025-07-30 14:42:25.972","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.entity.AliyunAk","line":"148","traceId":"","appName":"AppTokenServer","message":"Successfully decrypted Aliyun AK configuration - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ"}
{"time":"2025-07-30 14:42:25.972","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AliyunAkServiceImpl","line":"106","traceId":"","appName":"AppTokenServer","message":"Retrieved and decrypted enabled Aliyun AK configuration by token - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ, Status: 1"}
{"time":"2025-07-30 14:42:25.985","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 2093ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.028","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 41ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.028","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"227","traceId":"","appName":"AppTokenServer","message":"Cache miss - Generating new token - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.028","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"253","traceId":"","appName":"AppTokenServer","message":"Protocol validation time: 0ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.028","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"257","traceId":"","appName":"AppTokenServer","message":"Calling STS service - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.030","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"176","traceId":"","appName":"AppTokenServer","message":"Generated new session name: sts-4OlNAY8K-20250730144226"}
{"time":"2025-07-30 14:42:26.031","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"46","traceId":"","appName":"AppTokenServer","message":"Starting STS assume role operation - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-4OlNAY8K-20250730144226 (auto-generated: true), Duration: 3600s, Protocol: HTTPS"}
{"time":"2025-07-30 14:42:26.063","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.PolicyServiceImpl","line":"301","traceId":"","appName":"AppTokenServer","message":"Found policy by name: write_policy -> ID: 4"}
{"time":"2025-07-30 14:42:26.063","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"69","traceId":"","appName":"AppTokenServer","message":"Applied policy: policy name: write_policy"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"83","traceId":"","appName":"AppTokenServer","message":"STS assume role operation completed successfully - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-4OlNAY8K-20250730144226"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"271","traceId":"","appName":"AppTokenServer","message":"STS service call time: 596ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"272","traceId":"","appName":"AppTokenServer","message":"STS token generated successfully - StatusCode: 200, AccessKeyId: STS.NYcB***, Expiration: 2025-07-30T15:42:26Z - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"291","traceId":"","appName":"AppTokenServer","message":"Cache async setup time: 0ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"300","traceId":"","appName":"AppTokenServer","message":"Response header creation time: 0ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"304","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Completed Successfully - RequestId: REQ-1753857743890-94, Total Time: 2734ms ==="}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"305","traceId":"","appName":"AppTokenServer","message":"Time breakdown - Config: N/Ams, Cache Check: N/Ams, STS Call: 596ms, Total: 2734ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: ee3e8e1dc6b645e384a5ea31498d0da2 (Generated)"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  request-start-time: 1753857743739"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-30 14:42:26.635","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-30 14:42:26.635","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-30 14:42:26.635","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-30 14:42:26.635","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:42:26.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-30 14:42:26.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: ee3e8e1dc6b645e384a5ea31498d0da2"}
{"time":"2025-07-30 14:42:26.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Trace-ID: ee3e8e1dc6b645e384a5ea31498d0da2"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Cache-Status: MISS"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3599"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Response-Time: 1753857746624"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-STS-Call-Time: 596"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  Date: Wed, 30 Jul 2025 06:42:26 GMT"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-30 14:42:26.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-30 14:42:26.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-30 14:42:26.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-30 14:42:26.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: ee3e8e1dc6b645e384a5ea31498d0da2 ==="}
{"time":"2025-07-30 14:42:26.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"143","traceId":"","appName":"AppTokenServer","message":"Successfully cached STS token with key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5 for 600 seconds"}
{"time":"2025-07-30 14:42:26.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"285","traceId":"","appName":"AppTokenServer","message":"Async cache put time: 45ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:45:50.789","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-30 14:45:50.993","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-30 14:45:51.014","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown initiated..."}
{"time":"2025-07-30 14:45:51.018","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown completed."}
{"time":"2025-07-30 14:51:12.078","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-30 14:51:12.107","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 59844 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-30 14:51:12.108","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-30 14:51:12.723","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-30 14:51:12.725","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-30 14:51:12.748","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-30 14:51:13.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 80 (http)"}
{"time":"2025-07-30 14:51:13.217","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-80\"]"}
{"time":"2025-07-30 14:51:13.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-30 14:51:13.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-30 14:51:13.264","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-30 14:51:13.265","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1132 ms"}
{"time":"2025-07-30 14:51:13.630","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-30 14:51:13.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-30 14:51:13.777","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-30 14:51:13.781","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-30 14:51:13.783","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-30 14:51:13.785","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-30 14:51:14.214","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-30 14:51:14.253","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-80\"]"}
{"time":"2025-07-30 14:51:14.265","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 80 (http) with context path '/'"}
{"time":"2025-07-30 14:51:14.275","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.52 seconds (process running for 2.935)"}
{"time":"2025-07-30 14:51:14.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-30 14:51:14.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-30 14:51:14.735","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 1 ms"}
{"time":"2025-07-30 14:51:14.757","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Starting..."}
{"time":"2025-07-30 14:51:15.046","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@3dec2499"}
{"time":"2025-07-30 14:51:15.048","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Start completed."}
{"time":"2025-07-30 14:51:27.545","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-30 14:51:27.545","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 8753cfcf2c6b4848b29005995aa7a49c (Generated)"}
{"time":"2025-07-30 14:51:27.546","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-30 14:51:27.546","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-30 14:51:27.546","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-30 14:51:27.546","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:51:27.546","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"  request-start-time: 1753858287479"}
{"time":"2025-07-30 14:51:27.546","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-30 14:51:27.546","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-30 14:51:27.546","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-30 14:51:27.547","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-30 14:51:27.547","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"8753cfcf2c6b4848b29005995aa7a49c","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:51:27.620","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-30 14:51:27.621","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753858287617-93 ==="}
{"time":"2025-07-30 14:51:27.621","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-30 14:51:29.593","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"329","traceId":"","appName":"AppTokenServer","message":"AES decrypting Aliyun configuration"}
{"time":"2025-07-30 14:51:29.606","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"336","traceId":"","appName":"AppTokenServer","message":"Aliyun configuration AES decryption completed - AccessKeyID length: 24, AccessKeySecret length: 30, RoleArn length: 41"}
{"time":"2025-07-30 14:51:29.607","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.entity.AliyunAk","line":"148","traceId":"","appName":"AppTokenServer","message":"Successfully decrypted Aliyun AK configuration - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ"}
{"time":"2025-07-30 14:51:29.607","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AliyunAkServiceImpl","line":"106","traceId":"","appName":"AppTokenServer","message":"Retrieved and decrypted enabled Aliyun AK configuration by token - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ, Status: 1"}
{"time":"2025-07-30 14:51:29.616","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 1994ms - RequestId: REQ-1753858287617-93"}
{"time":"2025-07-30 14:51:29.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"111","traceId":"","appName":"AppTokenServer","message":"Cache hit for key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5 - Retrieved valid STS token from cache, expires at: 2025-07-30T15:42:26Z"}
{"time":"2025-07-30 14:51:29.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 18ms - RequestId: REQ-1753858287617-93"}
{"time":"2025-07-30 14:51:29.635","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"229","traceId":"","appName":"AppTokenServer","message":"=== Cache Hit - Request Completed - RequestId: REQ-1753858287617-93, Total Time: 2018ms ==="}
{"time":"2025-07-30 14:51:29.642","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-30 14:51:29.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 68c66b6ee66e49dabfcb8eee7846f738 (Generated)"}
{"time":"2025-07-30 14:51:29.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-30 14:51:29.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-30 14:51:29.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-30 14:51:29.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:51:29.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  request-start-time: 1753858287479"}
{"time":"2025-07-30 14:51:29.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-30 14:51:29.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-30 14:51:29.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-30 14:51:29.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-30 14:51:29.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:51:29.653","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-30 14:51:29.653","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: 68c66b6ee66e49dabfcb8eee7846f738"}
{"time":"2025-07-30 14:51:29.653","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  X-Trace-ID: 68c66b6ee66e49dabfcb8eee7846f738"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  X-Cache-Status: HIT"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3056"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  X-Response-Time: 1753858289635"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753858287617-93"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  Date: Wed, 30 Jul 2025 06:51:29 GMT"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-30 14:51:29.654","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"68c66b6ee66e49dabfcb8eee7846f738","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: 68c66b6ee66e49dabfcb8eee7846f738 ==="}
{"time":"2025-07-30 14:53:53.874","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-30 14:53:54.137","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-30 14:53:54.194","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown initiated..."}
{"time":"2025-07-30 14:53:54.200","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown completed."}
{"time":"2025-07-30 14:55:05.675","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-30 14:55:05.705","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 78436 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-30 14:55:05.705","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-30 14:55:06.307","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-30 14:55:06.309","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-30 14:55:06.332","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-30 14:55:06.747","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 80 (http)"}
{"time":"2025-07-30 14:55:06.755","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-80\"]"}
{"time":"2025-07-30 14:55:06.757","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-30 14:55:06.758","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-30 14:55:06.806","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-30 14:55:06.806","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1074 ms"}
{"time":"2025-07-30 14:55:07.170","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-30 14:55:07.182","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-30 14:55:07.280","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-30 14:55:07.282","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-30 14:55:07.284","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-30 14:55:07.287","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-30 14:55:07.721","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-30 14:55:07.758","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-80\"]"}
{"time":"2025-07-30 14:55:07.769","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 80 (http) with context path '/'"}
{"time":"2025-07-30 14:55:07.780","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.421 seconds (process running for 2.81)"}
{"time":"2025-07-30 14:55:08.288","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-30 14:55:08.288","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-30 14:55:08.289","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(1)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 1 ms"}
{"time":"2025-07-30 14:55:08.325","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Starting..."}
{"time":"2025-07-30 14:55:08.637","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@62ae35e1"}
{"time":"2025-07-30 14:55:08.667","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(4)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Start completed."}
{"time":"2025-07-30 14:55:15.077","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-30 14:55:15.078","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: bed39ab6d644494ca56b22bf696e41c9 (Generated)"}
{"time":"2025-07-30 14:55:15.078","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-30 14:55:15.078","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-30 14:55:15.078","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-30 14:55:15.078","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:55:15.078","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"  request-start-time: 1753858515019"}
{"time":"2025-07-30 14:55:15.078","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-30 14:55:15.078","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-30 14:55:15.079","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-30 14:55:15.079","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-30 14:55:15.079","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"bed39ab6d644494ca56b22bf696e41c9","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:55:15.149","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-30 14:55:15.149","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753858515147-93 ==="}
{"time":"2025-07-30 14:55:15.149","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-30 14:55:16.724","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"329","traceId":"","appName":"AppTokenServer","message":"AES decrypting Aliyun configuration"}
{"time":"2025-07-30 14:55:16.733","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"336","traceId":"","appName":"AppTokenServer","message":"Aliyun configuration AES decryption completed - AccessKeyID length: 24, AccessKeySecret length: 30, RoleArn length: 41"}
{"time":"2025-07-30 14:55:16.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.entity.AliyunAk","line":"148","traceId":"","appName":"AppTokenServer","message":"Successfully decrypted Aliyun AK configuration - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ"}
{"time":"2025-07-30 14:55:16.734","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AliyunAkServiceImpl","line":"106","traceId":"","appName":"AppTokenServer","message":"Retrieved and decrypted enabled Aliyun AK configuration by token - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ, Status: 1"}
{"time":"2025-07-30 14:55:16.742","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 1593ms - RequestId: REQ-1753858515147-93"}
{"time":"2025-07-30 14:55:16.757","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 13ms - RequestId: REQ-1753858515147-93"}
{"time":"2025-07-30 14:55:16.757","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"236","traceId":"","appName":"AppTokenServer","message":"Cache miss - Generating new token - RequestId: REQ-1753858515147-93"}
{"time":"2025-07-30 14:55:16.757","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"262","traceId":"","appName":"AppTokenServer","message":"Protocol validation time: 0ms - RequestId: REQ-1753858515147-93"}
{"time":"2025-07-30 14:55:16.757","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"266","traceId":"","appName":"AppTokenServer","message":"Calling STS service - RequestId: REQ-1753858515147-93"}
{"time":"2025-07-30 14:55:16.758","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"190","traceId":"","appName":"AppTokenServer","message":"Generated new session name: sts-vpRn4G8m-20250730145516"}
{"time":"2025-07-30 14:55:16.758","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"238","traceId":"","appName":"AppTokenServer","message":"Starting STS assume role operation with request start time - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-vpRn4G8m-20250730145516 (auto-generated: true), Duration: 3600s, Protocol: HTTPS"}
{"time":"2025-07-30 14:55:16.778","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.PolicyServiceImpl","line":"301","traceId":"","appName":"AppTokenServer","message":"Found policy by name: write_policy -> ID: 4"}
{"time":"2025-07-30 14:55:16.780","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"261","traceId":"","appName":"AppTokenServer","message":"Applied policy: policy name: write_policy"}
{"time":"2025-07-30 14:55:17.200","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"275","traceId":"","appName":"AppTokenServer","message":"STS assume role operation completed successfully - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-vpRn4G8m-20250730145516"}
{"time":"2025-07-30 14:55:17.200","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"284","traceId":"","appName":"AppTokenServer","message":"STS service call time: 443ms - RequestId: REQ-1753858515147-93"}
{"time":"2025-07-30 14:55:17.200","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"285","traceId":"","appName":"AppTokenServer","message":"STS token generated successfully - StatusCode: 200, AccessKeyId: STS.NXZS***, Expiration: 2025-07-30T15:55:17Z - RequestId: REQ-1753858515147-93"}
{"time":"2025-07-30 14:55:17.201","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"304","traceId":"","appName":"AppTokenServer","message":"Cache async setup time: 1ms - RequestId: REQ-1753858515147-93"}
{"time":"2025-07-30 14:55:17.201","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"313","traceId":"","appName":"AppTokenServer","message":"Response header creation time: 0ms - RequestId: REQ-1753858515147-93"}
{"time":"2025-07-30 14:55:17.201","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"317","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Completed Successfully - RequestId: REQ-1753858515147-93, Total Time: 2054ms ==="}
{"time":"2025-07-30 14:55:17.201","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"318","traceId":"","appName":"AppTokenServer","message":"Time breakdown - Config: N/Ams, Cache Check: N/Ams, STS Call: 443ms, Total: 2054ms - RequestId: REQ-1753858515147-93"}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: f0ca58e4a0354711b1a23e3f5e44f0f8 (Generated)"}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  request-start-time: 1753858515019"}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-30 14:55:17.209","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:55:17.217","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-30 14:55:17.217","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: f0ca58e4a0354711b1a23e3f5e44f0f8"}
{"time":"2025-07-30 14:55:17.217","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  X-Trace-ID: f0ca58e4a0354711b1a23e3f5e44f0f8"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  X-Cache-Status: MISS"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3599"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  X-Response-Time: 1753858517201"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753858515147-93"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  X-STS-Call-Time: 443"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  Date: Wed, 30 Jul 2025 06:55:17 GMT"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-30 14:55:17.218","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-2","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"f0ca58e4a0354711b1a23e3f5e44f0f8","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: f0ca58e4a0354711b1a23e3f5e44f0f8 ==="}
{"time":"2025-07-30 14:55:17.231","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"143","traceId":"","appName":"AppTokenServer","message":"Successfully cached STS token with key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5 for 600 seconds"}
{"time":"2025-07-30 14:55:17.231","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"298","traceId":"","appName":"AppTokenServer","message":"Async cache put time: 30ms - RequestId: REQ-1753858515147-93"}
{"time":"2025-07-30 15:02:36.743","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-30 15:02:36.953","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-30 15:02:36.971","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown initiated..."}
{"time":"2025-07-30 15:02:36.974","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown completed."}
