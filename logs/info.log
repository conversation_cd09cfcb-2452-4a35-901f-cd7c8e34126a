{"time":"2025-07-30 14:41:44.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"background-preinit","stack_trace":"","class":"org.hibernate.validator.internal.util.Version","line":"21","traceId":"","appName":"AppTokenServer","message":"HV000001: Hibernate Validator 8.0.2.Final"}
{"time":"2025-07-30 14:41:44.670","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"53","traceId":"","appName":"AppTokenServer","message":"Starting StsServerApplication using Java 17.0.15 with PID 72880 (C:\Users\<USER>\soft\sts-server1\target\classes started by tom in C:\Users\<USER>\soft\sts-server1)"}
{"time":"2025-07-30 14:41:44.670","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"652","traceId":"","appName":"AppTokenServer","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"time":"2025-07-30 14:41:45.379","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"296","traceId":"","appName":"AppTokenServer","message":"Multiple Spring Data modules found, entering strict repository configuration mode"}
{"time":"2025-07-30 14:41:45.382","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"147","traceId":"","appName":"AppTokenServer","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"time":"2025-07-30 14:41:45.406","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","line":"215","traceId":"","appName":"AppTokenServer","message":"Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces."}
{"time":"2025-07-30 14:41:45.866","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"111","traceId":"","appName":"AppTokenServer","message":"Tomcat initialized with port 80 (http)"}
{"time":"2025-07-30 14:41:45.875","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing ProtocolHandler [\"http-nio-80\"]"}
{"time":"2025-07-30 14:41:45.876","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardService","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting service [Tomcat]"}
{"time":"2025-07-30 14:41:45.876","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.StandardEngine","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting Servlet engine: [Apache Tomcat/10.1.42]"}
{"time":"2025-07-30 14:41:45.960","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring embedded WebApplicationContext"}
{"time":"2025-07-30 14:41:45.960","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","line":"301","traceId":"","appName":"AppTokenServer","message":"Root WebApplicationContext: initialization completed in 1255 ms"}
{"time":"2025-07-30 14:41:46.367","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"40","traceId":"","appName":"AppTokenServer","message":"Initializing Redis Cache Manager"}
{"time":"2025-07-30 14:41:46.388","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.CacheConfig","line":"73","traceId":"","appName":"AppTokenServer","message":"Redis Cache Manager initialized with 3 cache configurations"}
{"time":"2025-07-30 14:41:46.507","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"57","traceId":"","appName":"AppTokenServer","message":"High concurrency task executor initialized - CorePoolSize: 32, MaxPoolSize: 200, QueueCapacity: 10000"}
{"time":"2025-07-30 14:41:46.510","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"84","traceId":"","appName":"AppTokenServer","message":"STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000"}
{"time":"2025-07-30 14:41:46.512","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"108","traceId":"","appName":"AppTokenServer","message":"Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000"}
{"time":"2025-07-30 14:41:46.514","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.config.HighConcurrencyConfig","line":"132","traceId":"","appName":"AppTokenServer","message":"Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000"}
{"time":"2025-07-30 14:41:46.989","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","line":"60","traceId":"","appName":"AppTokenServer","message":"Exposing 3 endpoints beneath base path '/actuator'"}
{"time":"2025-07-30 14:41:47.023","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.apache.coyote.http11.Http11NioProtocol","line":"168","traceId":"","appName":"AppTokenServer","message":"Starting ProtocolHandler [\"http-nio-80\"]"}
{"time":"2025-07-30 14:41:47.037","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","line":"243","traceId":"","appName":"AppTokenServer","message":"Tomcat started on port 80 (http) with context path '/'"}
{"time":"2025-07-30 14:41:47.046","ip":"DESKTOP-G92L71C","level":"INFO","thread":"main","stack_trace":"","class":"com.imile.stsserver.StsServerApplication","line":"59","traceId":"","appName":"AppTokenServer","message":"Started StsServerApplication in 2.843 seconds (process running for 3.366)"}
{"time":"2025-07-30 14:41:47.303","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"109","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Starting..."}
{"time":"2025-07-30 14:41:47.321","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","line":"168","traceId":"","appName":"AppTokenServer","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'"}
{"time":"2025-07-30 14:41:47.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"532","traceId":"","appName":"AppTokenServer","message":"Initializing Servlet 'dispatcherServlet'"}
{"time":"2025-07-30 14:41:47.322","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(3)-**************","stack_trace":"","class":"org.springframework.web.servlet.DispatcherServlet","line":"554","traceId":"","appName":"AppTokenServer","message":"Completed initialization in 0 ms"}
{"time":"2025-07-30 14:41:47.986","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.pool.HikariPool","line":"554","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@4e13d8eb"}
{"time":"2025-07-30 14:41:47.989","ip":"DESKTOP-G92L71C","level":"INFO","thread":"RMI TCP Connection(2)-**************","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"122","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Start completed."}
{"time":"2025-07-30 14:42:23.804","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: 8ae1b621ef66479bbc5fa21d1cb2a5c7 (Generated)"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  request-start-time: 1753857743739"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-30 14:42:23.805","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-1","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"8ae1b621ef66479bbc5fa21d1cb2a5c7","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:42:23.891","ip":"DESKTOP-G92L71C","level":"WARN","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.IpUtil","line":"86","traceId":"","appName":"AppTokenServer","message":"Unable to determine client IP, returning localhost"}
{"time":"2025-07-30 14:42:23.892","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"174","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Started - RequestId: REQ-1753857743890-94 ==="}
{"time":"2025-07-30 14:42:23.892","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"175","traceId":"","appName":"AppTokenServer","message":"Request Info - IP: 127.0.0.1, Token: 9733af12***, RoleArn: null, SessionName: null, Duration: 3600s"}
{"time":"2025-07-30 14:42:25.952","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"329","traceId":"","appName":"AppTokenServer","message":"AES decrypting Aliyun configuration"}
{"time":"2025-07-30 14:42:25.972","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.util.EncryptionUtil","line":"336","traceId":"","appName":"AppTokenServer","message":"Aliyun configuration AES decryption completed - AccessKeyID length: 24, AccessKeySecret length: 30, RoleArn length: 41"}
{"time":"2025-07-30 14:42:25.972","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.entity.AliyunAk","line":"148","traceId":"","appName":"AppTokenServer","message":"Successfully decrypted Aliyun AK configuration - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ"}
{"time":"2025-07-30 14:42:25.972","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AliyunAkServiceImpl","line":"106","traceId":"","appName":"AppTokenServer","message":"Retrieved and decrypted enabled Aliyun AK configuration by token - ID: 2, AccessKeyID: LTAI5tKz***j8NQSVsQ, Status: 1"}
{"time":"2025-07-30 14:42:25.985","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"186","traceId":"","appName":"AppTokenServer","message":"Config retrieval time: 2093ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.028","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"212","traceId":"","appName":"AppTokenServer","message":"Cache check time: 41ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.028","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"227","traceId":"","appName":"AppTokenServer","message":"Cache miss - Generating new token - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.028","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"253","traceId":"","appName":"AppTokenServer","message":"Protocol validation time: 0ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.028","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"257","traceId":"","appName":"AppTokenServer","message":"Calling STS service - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.030","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"176","traceId":"","appName":"AppTokenServer","message":"Generated new session name: sts-4OlNAY8K-20250730144226"}
{"time":"2025-07-30 14:42:26.031","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"46","traceId":"","appName":"AppTokenServer","message":"Starting STS assume role operation - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-4OlNAY8K-20250730144226 (auto-generated: true), Duration: 3600s, Protocol: HTTPS"}
{"time":"2025-07-30 14:42:26.063","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.PolicyServiceImpl","line":"301","traceId":"","appName":"AppTokenServer","message":"Found policy by name: write_policy -> ID: 4"}
{"time":"2025-07-30 14:42:26.063","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"69","traceId":"","appName":"AppTokenServer","message":"Applied policy: policy name: write_policy"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.service.impl.AppTokenServerServiceImpl","line":"83","traceId":"","appName":"AppTokenServer","message":"STS assume role operation completed successfully - Role: acs:ram::1653466991022848:role/ramslsuser, Session: sts-4OlNAY8K-20250730144226"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"271","traceId":"","appName":"AppTokenServer","message":"STS service call time: 596ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"272","traceId":"","appName":"AppTokenServer","message":"STS token generated successfully - StatusCode: 200, AccessKeyId: STS.NYcB***, Expiration: 2025-07-30T15:42:26Z - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"291","traceId":"","appName":"AppTokenServer","message":"Cache async setup time: 0ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"300","traceId":"","appName":"AppTokenServer","message":"Response header creation time: 0ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"304","traceId":"","appName":"AppTokenServer","message":"=== STS Token Request Completed Successfully - RequestId: REQ-1753857743890-94, Total Time: 2734ms ==="}
{"time":"2025-07-30 14:42:26.624","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-1","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"305","traceId":"","appName":"AppTokenServer","message":"Time breakdown - Config: N/Ams, Cache Check: N/Ams, STS Call: 596ms, Total: 2734ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"40","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"=== HTTP Request Started ==="}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"41","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Request Details - Method: POST, URI: /api/sts/token, TraceID: ee3e8e1dc6b645e384a5ea31498d0da2 (Generated)"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"48","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Request Headers:"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  accept: application/json, text/plain, */*"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  content-type: application/json"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  user-agent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  request-start-time: 1753857743739"}
{"time":"2025-07-30 14:42:26.634","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  content-length: 81"}
{"time":"2025-07-30 14:42:26.635","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  accept-encoding: gzip, compress, deflate, br"}
{"time":"2025-07-30 14:42:26.635","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  host: 127.0.0.1"}
{"time":"2025-07-30 14:42:26.635","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"53","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  connection: keep-alive"}
{"time":"2025-07-30 14:42:26.635","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"73","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Client Info - RemoteAddr: 127.0.0.1, UserAgent: bruno-runtime/2.7.0"}
{"time":"2025-07-30 14:42:26.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"87","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"=== HTTP Request Completed ==="}
{"time":"2025-07-30 14:42:26.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"88","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Response Details - Method: POST, URI: /api/sts/token, Status: 200, TraceID: ee3e8e1dc6b645e384a5ea31498d0da2"}
{"time":"2025-07-30 14:42:26.643","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"95","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Response Headers:"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Trace-ID: ee3e8e1dc6b645e384a5ea31498d0da2"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Cache-Status: MISS"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Cache-Key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Token-Remaining-Time: 3599"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Response-Time: 1753857746624"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Server-Name: AppTokenServer"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-API-Version: v1.0"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-Request-Id: REQ-1753857743890-94"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  X-STS-Call-Time: 596"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  Content-Type: application/json"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  Transfer-Encoding: chunked"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  Date: Wed, 30 Jul 2025 06:42:26 GMT"}
{"time":"2025-07-30 14:42:26.644","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  Keep-Alive: timeout=60"}
{"time":"2025-07-30 14:42:26.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"97","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"  Connection: keep-alive"}
{"time":"2025-07-30 14:42:26.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"103","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Request completed successfully - Status: 200"}
{"time":"2025-07-30 14:42:26.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"121","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"Request completed without exceptions"}
{"time":"2025-07-30 14:42:26.645","ip":"DESKTOP-G92L71C","level":"INFO","thread":"http-nio-80-exec-3","stack_trace":"","class":"com.imile.stsserver.interceptor.TraceIdInterceptor","line":"124","traceId":"ee3e8e1dc6b645e384a5ea31498d0da2","appName":"AppTokenServer","message":"=== HTTP Request Processing Finished - TraceID: ee3e8e1dc6b645e384a5ea31498d0da2 ==="}
{"time":"2025-07-30 14:42:26.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.service.StsTokenCacheService","line":"143","traceId":"","appName":"AppTokenServer","message":"Successfully cached STS token with key: sts:token:v1:6321ae71633845bd57cf63f7009a0fb5 for 600 seconds"}
{"time":"2025-07-30 14:42:26.669","ip":"DESKTOP-G92L71C","level":"INFO","thread":"ForkJoinPool.commonPool-worker-15","stack_trace":"","class":"com.imile.stsserver.controller.AppTokenServerController","line":"285","traceId":"","appName":"AppTokenServer","message":"Async cache put time: 45ms - RequestId: REQ-1753857743890-94"}
{"time":"2025-07-30 14:45:50.789","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"54","traceId":"","appName":"AppTokenServer","message":"Commencing graceful shutdown. Waiting for active requests to complete"}
{"time":"2025-07-30 14:45:50.993","ip":"DESKTOP-G92L71C","level":"INFO","thread":"tomcat-shutdown","stack_trace":"","class":"org.springframework.boot.web.embedded.tomcat.GracefulShutdown","line":"76","traceId":"","appName":"AppTokenServer","message":"Graceful shutdown complete"}
{"time":"2025-07-30 14:45:51.014","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"349","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown initiated..."}
{"time":"2025-07-30 14:45:51.018","ip":"DESKTOP-G92L71C","level":"INFO","thread":"SpringApplicationShutdownHook","stack_trace":"","class":"com.zaxxer.hikari.HikariDataSource","line":"351","traceId":"","appName":"AppTokenServer","message":"StsServerHikariCP - Shutdown completed."}
