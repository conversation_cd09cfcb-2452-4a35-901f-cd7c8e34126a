package com.imile.stsserver.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.imile.stsserver.model.sts.StsTokenResponse;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;

/**
 * STS Token Response 模型测试
 */
public class StsTokenResponseTest {

    @Test
    public void testTimestampConsistency() {
        StsTokenResponse response = new StsTokenResponse();
        
        // 设置当前时间
        Instant now = Instant.now();
        Instant expiration = now.plusSeconds(3600); // 1小时后过期
        
        // 设置请求开始时间
        LocalDateTime startDateTime = LocalDateTime.ofInstant(now, ZoneId.systemDefault());
        String isoStartTime = startDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
        response.setRequestStartTime(isoStartTime);
        response.setRequestStartTimestamp(now.getEpochSecond()); // 秒
        
        // 设置过期时间
        LocalDateTime expirationDateTime = LocalDateTime.ofInstant(expiration, ZoneId.systemDefault());
        String isoExpirationTime = expirationDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
        response.setExpiration(isoExpirationTime);
        response.setExpirationTimestamp(expiration.getEpochSecond()); // 秒
        
        // 验证时间戳都是以秒为单位
        assertNotNull(response.getRequestStartTimestamp());
        assertNotNull(response.getExpirationTimestamp());
        
        // 验证时间戳的差值符合预期（应该是3600秒）
        long timeDiff = response.getExpirationTimestamp() - response.getRequestStartTimestamp();
        assertEquals(3600L, timeDiff);
        
        System.out.println("Request Start Time: " + response.getRequestStartTime());
        System.out.println("Request Start Timestamp: " + response.getRequestStartTimestamp());
        System.out.println("Expiration Time: " + response.getExpiration());
        System.out.println("Expiration Timestamp: " + response.getExpirationTimestamp());
        System.out.println("Time Difference (seconds): " + timeDiff);
    }

    @Test
    public void testJsonSerialization() throws Exception {
        StsTokenResponse response = new StsTokenResponse();
        response.setStatusCode("200");
        response.setAccessKeyId("LTAI***");
        response.setAccessKeySecret("secret***");
        response.setSecurityToken("token***");
        
        // 设置时间字段
        Instant now = Instant.now();
        Instant expiration = now.plusSeconds(3600);
        
        LocalDateTime startDateTime = LocalDateTime.ofInstant(now, ZoneId.systemDefault());
        String isoStartTime = startDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
        response.setRequestStartTime(isoStartTime);
        response.setRequestStartTimestamp(now.getEpochSecond());
        
        LocalDateTime expirationDateTime = LocalDateTime.ofInstant(expiration, ZoneId.systemDefault());
        String isoExpirationTime = expirationDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
        response.setExpiration(isoExpirationTime);
        response.setExpirationTimestamp(expiration.getEpochSecond());
        
        // 序列化为JSON
        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(response);
        
        System.out.println("JSON Output:");
        System.out.println(json);
        
        // 验证JSON包含所有必要字段
        assertTrue(json.contains("RequestStartTime"));
        assertTrue(json.contains("RequestStartTimestamp"));
        assertTrue(json.contains("Expiration"));
        assertTrue(json.contains("ExpirationTimestamp"));
        
        // 反序列化验证
        StsTokenResponse deserialized = mapper.readValue(json, StsTokenResponse.class);
        assertEquals(response.getRequestStartTimestamp(), deserialized.getRequestStartTimestamp());
        assertEquals(response.getExpirationTimestamp(), deserialized.getExpirationTimestamp());
    }

    @Test
    public void testTimestampFormat() {
        StsTokenResponse response = new StsTokenResponse();
        
        // 测试特定的时间戳值
        long testTimestamp = 1640995200L; // 2022-01-01 00:00:00 UTC
        response.setRequestStartTimestamp(testTimestamp);
        response.setExpirationTimestamp(testTimestamp + 3600);
        
        // 验证时间戳格式
        assertEquals(10, String.valueOf(testTimestamp).length()); // 10位数字表示秒级时间戳
        assertEquals(10, String.valueOf(response.getExpirationTimestamp()).length());
        
        // 验证时间差
        long diff = response.getExpirationTimestamp() - response.getRequestStartTimestamp();
        assertEquals(3600L, diff);
        
        System.out.println("Test timestamp (seconds): " + testTimestamp);
        System.out.println("Expiration timestamp (seconds): " + response.getExpirationTimestamp());
        System.out.println("Difference (seconds): " + diff);
    }
}
