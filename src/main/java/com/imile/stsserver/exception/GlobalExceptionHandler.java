package com.imile.stsserver.exception;

import com.imile.stsserver.model.sts.StsTokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;

/**
 * Global Exception Handler for STS operations
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * Handle STS specific exceptions
     */
    @ExceptionHandler(StsException.class)
    public ResponseEntity<StsTokenResponse> handleStsException(StsException e) {
        log.error("=== Global Exception Handler - STS Exception ===");
        log.error("STS Exception Details - Code: {}, Message: {}, RequestId: {}",
                e.getErrorCode(), e.getMessage(), e.getRequestId());
        log.error("STS Exception Stack Trace:", e);

        StsTokenResponse response = new StsTokenResponse();
        response.setStatusCode("400");
        response.setErrorCode(e.getErrorCode());
        response.setErrorMessage(e.getMessage());
        response.setRequestId(e.getRequestId());

        log.error("Returning STS error response: {}", response);
        log.error("=== STS Exception Handling Completed ===");

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * Handle validation errors
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Map<String, Object>> handleValidationExceptions(
            MethodArgumentNotValidException ex) {

        log.error("=== Global Exception Handler - Validation Exception ===");
        log.error("Request validation failed");

        Map<String, Object> response = new HashMap<>();
        Map<String, String> errors = new HashMap<>();

        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            Object rejectedValue = ((FieldError) error).getRejectedValue();

            errors.put(fieldName, errorMessage);
            log.error("Validation error - Field: {}, Message: {}, RejectedValue: {}",
                    fieldName, errorMessage, rejectedValue);
        });

        response.put("success", false);
        response.put("errorCode", "VALIDATION_ERROR");
        response.put("errorMessage", "Validation failed");
        response.put("fieldErrors", errors);

        log.error("Validation exception stack trace:", ex);
        log.error("Returning validation error response: {}", response);
        log.error("=== Validation Exception Handling Completed ===");

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * Handle constraint violation exceptions
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Map<String, Object>> handleConstraintViolationException(
            ConstraintViolationException ex) {

        log.error("=== Global Exception Handler - Constraint Violation Exception ===");
        log.error("Constraint validation failed");

        Map<String, Object> response = new HashMap<>();
        Map<String, String> errors = new HashMap<>();

        for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            String fieldName = violation.getPropertyPath().toString();
            String errorMessage = violation.getMessage();
            Object invalidValue = violation.getInvalidValue();

            errors.put(fieldName, errorMessage);
            log.error("Constraint violation - Field: {}, Message: {}, InvalidValue: {}",
                    fieldName, errorMessage, invalidValue);
        }

        response.put("success", false);
        response.put("errorCode", "VALIDATION_ERROR");
        response.put("errorMessage", "Validation failed");
        response.put("fieldErrors", errors);

        log.error("Constraint violation exception stack trace:", ex);
        log.error("Returning constraint violation error response: {}", response);
        log.error("=== Constraint Violation Exception Handling Completed ===");

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * Handle missing request header exceptions
     */
    @ExceptionHandler(MissingRequestHeaderException.class)
    public ResponseEntity<Map<String, Object>> handleMissingRequestHeaderException(
            MissingRequestHeaderException ex) {

        log.error("=== Global Exception Handler - Missing Request Header Exception ===");
        log.error("Required header missing - Header: {}", ex.getHeaderName());
        log.error("Missing request header exception stack trace:", ex);

        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", "MISSING_HEADER");
        response.put("errorMessage", "Required header missing: " + ex.getHeaderName());

        log.error("Returning missing header error response: {}", response);
        log.error("=== Missing Request Header Exception Handling Completed ===");

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * Handle general exceptions
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGeneralException(Exception e) {
        log.error("=== Global Exception Handler - General Exception ===");
        log.error("Unexpected error occurred - Type: {}, Message: {}",
                e.getClass().getSimpleName(), e.getMessage());
        log.error("General exception stack trace:", e);

        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", "INTERNAL_ERROR");
        response.put("errorMessage", "Internal server error occurred");

        log.error("Returning general error response: {}", response);
        log.error("=== General Exception Handling Completed ===");

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
