package com.imile.stsserver.exception;

/**
 * Custom STS Exception
 */
public class StsException extends RuntimeException {
    
    private String errorCode;
    private String requestId;
    
    public StsException(String message) {
        super(message);
    }
    
    public StsException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public StsException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public StsException(String errorCode, String message, String requestId) {
        super(message);
        this.errorCode = errorCode;
        this.requestId = requestId;
    }
    
    public StsException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getRequestId() {
        return requestId;
    }
}
