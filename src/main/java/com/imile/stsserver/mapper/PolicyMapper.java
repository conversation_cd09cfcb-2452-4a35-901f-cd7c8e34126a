package com.imile.stsserver.mapper;

import com.imile.stsserver.entity.Policy;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 策略Mapper
 */
@Mapper
public interface PolicyMapper {

    /**
     * 根据ID查询策略
     */
    @Select("SELECT id, name, display_name as displayName, content, description, type, " +
            "status, create_date as createDate, last_upd_date as lastUpdDate " +
            "FROM policy WHERE id = #{id}")
    Policy selectById(@Param("id") Long id);

    /**
     * 根据名称查询策略
     */
    @Select("SELECT id, name, display_name as displayName, content, description, type, " +
            "status, create_date as createDate, last_upd_date as lastUpdDate " +
            "FROM policy WHERE name = #{name}")
    Policy selectByName(@Param("name") String name);

    /**
     * 根据名称查询启用的策略
     */
    @Select("SELECT id, name, display_name as displayName, content, description, type, " +
            "status, create_date as createDate, last_upd_date as lastUpdDate " +
            "FROM policy WHERE name = #{name} AND status = 1")
    Policy selectByNameAndEnabled(@Param("name") String name);

    /**
     * 查询所有策略
     */
    @Select("SELECT id, name, display_name as displayName, content, description, type, " +
            "status, create_date as createDate, last_upd_date as lastUpdDate " +
            "FROM policy ORDER BY id ASC")
    List<Policy> selectAll();

    /**
     * 查询所有启用的策略
     */
    @Select("SELECT id, name, display_name as displayName, content, description, type, " +
            "status, create_date as createDate, last_upd_date as lastUpdDate " +
            "FROM policy WHERE status = 1 ORDER BY id ASC")
    List<Policy> selectAllEnabled();

    /**
     * 根据类型查询策略
     */
    @Select("SELECT id, name, display_name as displayName, content, description, type, " +
            "status, create_date as createDate, last_upd_date as lastUpdDate " +
            "FROM policy WHERE type = #{type} ORDER BY id ASC")
    List<Policy> selectByType(@Param("type") String type);

    /**
     * 插入新策略
     */
    @Insert("INSERT INTO policy (name, display_name, content, description, type, status, create_date) " +
            "VALUES (#{name}, #{displayName}, #{content}, #{description}, #{type}, #{status}, NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Policy policy);

    /**
     * 更新策略
     */
    @Update("UPDATE policy SET " +
            "display_name = #{displayName}, " +
            "content = #{content}, " +
            "description = #{description}, " +
            "type = #{type}, " +
            "status = #{status}, " +
            "last_upd_date = NOW() " +
            "WHERE id = #{id}")
    int updateById(Policy policy);

    /**
     * 更新策略状态
     */
    @Update("UPDATE policy SET status = #{status}, last_upd_date = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 删除策略
     */
    @Delete("DELETE FROM policy WHERE id = #{id}")
    int deleteById(@Param("id") Long id);

    /**
     * 检查策略名称是否存在
     */
    @Select("SELECT COUNT(*) FROM policy WHERE name = #{name}")
    int existsByName(@Param("name") String name);

    /**
     * 统计启用的策略数量
     */
    @Select("SELECT COUNT(*) FROM policy WHERE status = 1")
    int countEnabled();
}
