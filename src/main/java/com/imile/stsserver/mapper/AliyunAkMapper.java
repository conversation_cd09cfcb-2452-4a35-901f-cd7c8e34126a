package com.imile.stsserver.mapper;

import com.imile.stsserver.entity.AliyunAk;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 阿里云AccessKey配置Mapper
 */
@Mapper
public interface AliyunAkMapper {

    /**
     * 根据ID查询
     */
    @Select("SELECT id, AccessKeyID_encrypted as accessKeyIdEncrypted, " +
            "AccessKeySecret_encrypted as accessKeySecretEncrypted, " +
            "RoleArn_encrypted as roleArnEncrypted, AccessKeyID_hash as accessKeyIdHash, " +
            "createdate, last_upd_date as lastUpdDate, status, description " +
            "FROM aliyun_ak WHERE id = #{id}")
    AliyunAk selectById(@Param("id") Long id);

    /**
     * 根据AccessKeyID哈希查询
     */
    @Select("SELECT id, AccessKeyID_encrypted as accessKeyIdEncrypted, " +
            "AccessKeySecret_encrypted as accessKeySecretEncrypted, " +
            "RoleArn_encrypted as roleArnEncrypted, AccessKeyID_hash as accessKeyIdHash, " +
            "createdate, last_upd_date as lastUpdDate, status, description " +
            "FROM aliyun_ak WHERE AccessKeyID_hash = #{accessKeyIdHash}")
    AliyunAk selectByAccessKeyIdHash(@Param("accessKeyIdHash") String accessKeyIdHash);

    /**
     * 根据token（AccessKeyID_hash）查询启用的配置
     */
    @Select("SELECT id, AccessKeyID_encrypted as accessKeyIdEncrypted, " +
            "AccessKeySecret_encrypted as accessKeySecretEncrypted, " +
            "RoleArn_encrypted as roleArnEncrypted, AccessKeyID_hash as accessKeyIdHash, " +
            "createdate, last_upd_date as lastUpdDate, status, description " +
            "FROM aliyun_ak WHERE AccessKeyID_hash = #{token} AND status = 1")
    AliyunAk selectByTokenAndEnabled(@Param("token") String token);

    /**
     * 查询第一个启用的配置
     */
    @Select("SELECT id, AccessKeyID_encrypted as accessKeyIdEncrypted, " +
            "AccessKeySecret_encrypted as accessKeySecretEncrypted, " +
            "RoleArn_encrypted as roleArnEncrypted, AccessKeyID_hash as accessKeyIdHash, " +
            "createdate, last_upd_date as lastUpdDate, status, description " +
            "FROM aliyun_ak WHERE status = 1 ORDER BY id ASC LIMIT 1")
    AliyunAk selectFirstEnabled();

    /**
     * 查询所有启用的配置
     */
    @Select("SELECT id, AccessKeyID_encrypted as accessKeyIdEncrypted, " +
            "AccessKeySecret_encrypted as accessKeySecretEncrypted, " +
            "RoleArn_encrypted as roleArnEncrypted, AccessKeyID_hash as accessKeyIdHash, " +
            "createdate, last_upd_date as lastUpdDate, status, description " +
            "FROM aliyun_ak WHERE status = 1 ORDER BY id ASC")
    List<AliyunAk> selectAllEnabled();

    /**
     * 查询所有配置
     */
    @Select("SELECT id, AccessKeyID_encrypted as accessKeyIdEncrypted, " +
            "AccessKeySecret_encrypted as accessKeySecretEncrypted, " +
            "RoleArn_encrypted as roleArnEncrypted, AccessKeyID_hash as accessKeyIdHash, " +
            "createdate, last_upd_date as lastUpdDate, status, description " +
            "FROM aliyun_ak ORDER BY id ASC")
    List<AliyunAk> selectAll();

    /**
     * 插入新配置
     */
    @Insert("INSERT INTO aliyun_ak (AccessKeyID_encrypted, AccessKeySecret_encrypted, " +
            "RoleArn_encrypted, AccessKeyID_hash, status, description) " +
            "VALUES (#{accessKeyIdEncrypted}, #{accessKeySecretEncrypted}, " +
            "#{roleArnEncrypted}, #{accessKeyIdHash}, #{status}, #{description})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(AliyunAk aliyunAk);

    /**
     * 更新配置
     */
    @Update("UPDATE aliyun_ak SET " +
            "AccessKeySecret_encrypted = #{accessKeySecretEncrypted}, " +
            "RoleArn_encrypted = #{roleArnEncrypted}, " +
            "status = #{status}, " +
            "description = #{description}, " +
            "last_upd_date = CURRENT_TIMESTAMP " +
            "WHERE id = #{id}")
    int updateById(AliyunAk aliyunAk);

    /**
     * 更新状态
     */
    @Update("UPDATE aliyun_ak SET status = #{status}, last_upd_date = CURRENT_TIMESTAMP WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 删除配置
     */
    @Delete("DELETE FROM aliyun_ak WHERE id = #{id}")
    int deleteById(@Param("id") Long id);

    /**
     * 统计启用的配置数量
     */
    @Select("SELECT COUNT(*) FROM aliyun_ak WHERE status = 1")
    int countEnabled();

    /**
     * 检查AccessKeyID哈希是否存在
     */
    @Select("SELECT COUNT(*) FROM aliyun_ak WHERE AccessKeyID_hash = #{accessKeyIdHash}")
    int existsByAccessKeyIdHash(@Param("accessKeyIdHash") String accessKeyIdHash);
}
