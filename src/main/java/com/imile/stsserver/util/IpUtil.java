package com.imile.stsserver.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * IP地址工具类
 * 用于获取客户端真实IP地址
 */
@Slf4j
public class IpUtil {

    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * 获取客户端真实IP地址
     * 考虑代理、负载均衡等情况
     * 
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        if (request == null) {
            log.warn("HttpServletRequest is null, returning localhost IP");
            return LOCALHOST_IPV4;
        }

        String ip = null;

        // 1. 检查X-Forwarded-For头（最常用的代理头）
        ip = request.getHeader("X-Forwarded-For");
        if (isValidIp(ip)) {
            // X-Forwarded-For可能包含多个IP，取第一个
            if (ip.contains(",")) {
                ip = ip.split(",")[0].trim();
            }
            log.debug("Got IP from X-Forwarded-For: {}", ip);
            return ip;
        }

        // 2. 检查X-Real-IP头（Nginx常用）
        ip = request.getHeader("X-Real-IP");
        if (isValidIp(ip)) {
            log.debug("Got IP from X-Real-IP: {}", ip);
            return ip;
        }

        // 3. 检查Proxy-Client-IP头
        ip = request.getHeader("Proxy-Client-IP");
        if (isValidIp(ip)) {
            log.debug("Got IP from Proxy-Client-IP: {}", ip);
            return ip;
        }

        // 4. 检查WL-Proxy-Client-IP头（WebLogic）
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (isValidIp(ip)) {
            log.debug("Got IP from WL-Proxy-Client-IP: {}", ip);
            return ip;
        }

        // 5. 检查HTTP_CLIENT_IP头
        ip = request.getHeader("HTTP_CLIENT_IP");
        if (isValidIp(ip)) {
            log.debug("Got IP from HTTP_CLIENT_IP: {}", ip);
            return ip;
        }

        // 6. 检查HTTP_X_FORWARDED_FOR头
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (isValidIp(ip)) {
            log.debug("Got IP from HTTP_X_FORWARDED_FOR: {}", ip);
            return ip;
        }

        // 7. 最后使用getRemoteAddr()
        ip = request.getRemoteAddr();
        if (isValidIp(ip)) {
            log.debug("Got IP from getRemoteAddr(): {}", ip);
            return ip;
        }

        log.warn("Unable to determine client IP, returning localhost");
        return LOCALHOST_IPV4;
    }

    /**
     * 验证IP地址是否有效
     * 
     * @param ip IP地址
     * @return 是否有效
     */
    private static boolean isValidIp(String ip) {
        return StringUtils.hasText(ip) && 
               !UNKNOWN.equalsIgnoreCase(ip) && 
               !isLocalhost(ip);
    }

    /**
     * 检查是否为本地地址
     * 
     * @param ip IP地址
     * @return 是否为本地地址
     */
    private static boolean isLocalhost(String ip) {
        return LOCALHOST_IPV4.equals(ip) || LOCALHOST_IPV6.equals(ip);
    }

    /**
     * 验证IP地址格式是否正确
     * 
     * @param ip IP地址
     * @return 是否为有效的IP格式
     */
    public static boolean isValidIpFormat(String ip) {
        if (!StringUtils.hasText(ip)) {
            return false;
        }

        // 简单的IPv4格式验证
        String ipv4Pattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        if (ip.matches(ipv4Pattern)) {
            return true;
        }

        // 简单的IPv6格式验证
        String ipv6Pattern = "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$";
        return ip.matches(ipv6Pattern);
    }

    /**
     * 获取IP地址的地理位置信息（简化版）
     * 
     * @param ip IP地址
     * @return 地理位置描述
     */
    public static String getIpLocation(String ip) {
        if (!isValidIpFormat(ip)) {
            return "Unknown";
        }

        if (isLocalhost(ip)) {
            return "Localhost";
        }

        // 简单的内网IP判断
        if (isPrivateIp(ip)) {
            return "Private Network";
        }

        return "Public Network";
    }

    /**
     * 判断是否为内网IP
     * 
     * @param ip IP地址
     * @return 是否为内网IP
     */
    public static boolean isPrivateIp(String ip) {
        if (!isValidIpFormat(ip)) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            int first = Integer.parseInt(parts[0]);
            int second = Integer.parseInt(parts[1]);

            // 10.0.0.0 - **************
            if (first == 10) {
                return true;
            }

            // ********** - **************
            if (first == 172 && second >= 16 && second <= 31) {
                return true;
            }

            // *********** - ***************
            if (first == 192 && second == 168) {
                return true;
            }

        } catch (NumberFormatException e) {
            log.warn("Invalid IP format for private IP check: {}", ip);
        }

        return false;
    }

    /**
     * 格式化IP地址用于日志输出
     * 对于内网IP直接显示，对于公网IP进行脱敏
     * 
     * @param ip IP地址
     * @return 格式化后的IP
     */
    public static String formatIpForLog(String ip) {
        if (!isValidIpFormat(ip)) {
            return "Invalid-IP";
        }

        if (isPrivateIp(ip) || isLocalhost(ip)) {
            return ip;
        }

        // 对公网IP进行脱敏：只显示前两段
        String[] parts = ip.split("\\.");
        if (parts.length == 4) {
            return parts[0] + "." + parts[1] + ".***.**";
        }

        return "***.***.***.**";
    }
}
