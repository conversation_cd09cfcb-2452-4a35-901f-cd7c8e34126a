package com.imile.stsserver.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * 加密工具类
 * 提供MD5加密和解密功能
 */
@Slf4j
public class EncryptionUtil {

    private static final String MD5_ALGORITHM = "MD5";
    private static final String AES_ALGORITHM = "AES";
    private static final String AES_TRANSFORMATION = "AES/ECB/PKCS5Padding";
    private static final String SALT = "demo5_aliyun_ak_salt_2025"; // 盐值，增加安全性

    // AES密钥（32字节，用于AES-256）
    private static final String AES_KEY = "Demo5AliyunAK2025SecretKey123456"; // 32字节密钥

    /**
     * MD5加密
     * 
     * @param plainText 明文
     * @return 加密后的字符串
     */
    public static String encryptMD5(String plainText) {
        if (!StringUtils.hasText(plainText)) {
            log.warn("Plain text is null or empty for MD5 encryption");
            return null;
        }

        try {
            // 添加盐值增加安全性
            String saltedText = plainText + SALT;
            
            MessageDigest md = MessageDigest.getInstance(MD5_ALGORITHM);
            byte[] hashBytes = md.digest(saltedText.getBytes(StandardCharsets.UTF_8));
            
            // 转换为Base64编码，便于存储
            String encrypted = Base64.getEncoder().encodeToString(hashBytes);
            
            log.debug("MD5 encryption completed for text length: {}", plainText.length());
            return encrypted;
            
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5 algorithm not available", e);
            throw new RuntimeException("MD5 encryption failed", e);
        }
    }

    /**
     * 验证明文是否与加密文本匹配
     * 由于MD5是单向加密，无法直接解密，只能验证
     * 
     * @param plainText 明文
     * @param encryptedText 加密文本
     * @return 是否匹配
     */
    public static boolean verifyMD5(String plainText, String encryptedText) {
        if (!StringUtils.hasText(plainText) || !StringUtils.hasText(encryptedText)) {
            log.warn("Plain text or encrypted text is null/empty for MD5 verification");
            return false;
        }

        try {
            String encryptedPlainText = encryptMD5(plainText);
            boolean matches = encryptedText.equals(encryptedPlainText);
            
            log.debug("MD5 verification result: {}", matches);
            return matches;
            
        } catch (Exception e) {
            log.error("MD5 verification failed", e);
            return false;
        }
    }

    /**
     * 批量加密阿里云配置
     * 
     * @param accessKeyId AccessKeyID
     * @param accessKeySecret AccessKeySecret
     * @param roleArn RoleArn
     * @return 加密后的配置数组 [encryptedAccessKeyId, encryptedAccessKeySecret, encryptedRoleArn]
     */
    public static String[] encryptAliyunConfig(String accessKeyId, String accessKeySecret, String roleArn) {
        log.info("Encrypting Aliyun configuration - AccessKeyID length: {}, AccessKeySecret length: {}, RoleArn length: {}", 
                accessKeyId != null ? accessKeyId.length() : 0,
                accessKeySecret != null ? accessKeySecret.length() : 0,
                roleArn != null ? roleArn.length() : 0);

        String[] encrypted = new String[3];
        encrypted[0] = encryptMD5(accessKeyId);
        encrypted[1] = encryptMD5(accessKeySecret);
        encrypted[2] = encryptMD5(roleArn);

        log.info("Aliyun configuration encryption completed");
        return encrypted;
    }

    /**
     * 验证阿里云配置
     * 
     * @param plainAccessKeyId 明文AccessKeyID
     * @param plainAccessKeySecret 明文AccessKeySecret
     * @param plainRoleArn 明文RoleArn
     * @param encryptedAccessKeyId 加密的AccessKeyID
     * @param encryptedAccessKeySecret 加密的AccessKeySecret
     * @param encryptedRoleArn 加密的RoleArn
     * @return 是否全部匹配
     */
    public static boolean verifyAliyunConfig(String plainAccessKeyId, String plainAccessKeySecret, String plainRoleArn,
                                           String encryptedAccessKeyId, String encryptedAccessKeySecret, String encryptedRoleArn) {
        log.debug("Verifying Aliyun configuration");

        boolean accessKeyIdMatch = verifyMD5(plainAccessKeyId, encryptedAccessKeyId);
        boolean accessKeySecretMatch = verifyMD5(plainAccessKeySecret, encryptedAccessKeySecret);
        boolean roleArnMatch = verifyMD5(plainRoleArn, encryptedRoleArn);

        boolean allMatch = accessKeyIdMatch && accessKeySecretMatch && roleArnMatch;
        
        log.info("Aliyun configuration verification - AccessKeyID: {}, AccessKeySecret: {}, RoleArn: {}, Overall: {}", 
                accessKeyIdMatch, accessKeySecretMatch, roleArnMatch, allMatch);

        return allMatch;
    }

    /**
     * 生成用于显示的脱敏字符串
     * 
     * @param originalText 原始文本
     * @param showLength 显示的字符长度
     * @return 脱敏后的字符串
     */
    public static String maskSensitiveData(String originalText, int showLength) {
        if (!StringUtils.hasText(originalText)) {
            return "***";
        }

        if (originalText.length() <= showLength * 2) {
            return "***";
        }

        return originalText.substring(0, showLength) + "***" + 
               originalText.substring(originalText.length() - showLength);
    }

    /**
     * 生成MD5哈希值（不加盐，用于缓存键等非敏感场景）
     * 
     * @param input 输入字符串
     * @return MD5哈希值
     */
    public static String generateMD5Hash(String input) {
        if (!StringUtils.hasText(input)) {
            return null;
        }

        try {
            MessageDigest md = MessageDigest.getInstance(MD5_ALGORITHM);
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
            
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5 algorithm not available for hash generation", e);
            throw new RuntimeException("MD5 hash generation failed", e);
        }
    }

    /**
     * 验证加密数据的完整性
     * 
     * @param encryptedData 加密数据
     * @return 是否为有效的加密数据
     */
    public static boolean isValidEncryptedData(String encryptedData) {
        if (!StringUtils.hasText(encryptedData)) {
            return false;
        }

        try {
            // 检查是否为有效的Base64编码
            Base64.getDecoder().decode(encryptedData);
            return true;
        } catch (IllegalArgumentException e) {
            log.warn("Invalid encrypted data format: {}", encryptedData);
            return false;
        }
    }

    /**
     * 获取加密数据的摘要信息（用于日志）
     * 
     * @param encryptedData 加密数据
     * @return 摘要信息
     */
    public static String getEncryptedDataSummary(String encryptedData) {
        if (!StringUtils.hasText(encryptedData)) {
            return "null";
        }

        if (encryptedData.length() <= 8) {
            return "***";
        }

        return encryptedData.substring(0, 4) + "***" + encryptedData.substring(encryptedData.length() - 4);
    }

    // ==================== AES 可逆加密方法 ====================

    /**
     * AES加密
     *
     * @param plainText 明文
     * @return 加密后的Base64字符串
     */
    public static String encryptAES(String plainText) {
        if (!StringUtils.hasText(plainText)) {
            log.warn("Plain text is null or empty for AES encryption");
            return null;
        }

        try {
            // 创建密钥
            SecretKeySpec secretKey = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), AES_ALGORITHM);

            // 创建加密器
            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);

            // 加密
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            // 转换为Base64
            String encrypted = Base64.getEncoder().encodeToString(encryptedBytes);

            log.debug("AES encryption completed for text length: {}", plainText.length());
            return encrypted;

        } catch (Exception e) {
            log.error("AES encryption failed", e);
            throw new RuntimeException("AES encryption failed", e);
        }
    }

    /**
     * AES解密
     *
     * @param encryptedText 加密的Base64字符串
     * @return 解密后的明文
     */
    public static String decryptAES(String encryptedText) {
        if (!StringUtils.hasText(encryptedText)) {
            log.warn("Encrypted text is null or empty for AES decryption");
            return null;
        }

        try {
            // 创建密钥
            SecretKeySpec secretKey = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), AES_ALGORITHM);

            // 创建解密器
            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);

            // Base64解码
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedText);

            // 解密
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);

            String decrypted = new String(decryptedBytes, StandardCharsets.UTF_8);

            log.debug("AES decryption completed for encrypted text length: {}", encryptedText.length());
            return decrypted;

        } catch (Exception e) {
            log.error("AES decryption failed for encrypted text: {}", getEncryptedDataSummary(encryptedText), e);
            throw new RuntimeException("AES decryption failed", e);
        }
    }

    /**
     * 批量AES加密阿里云配置
     *
     * @param accessKeyId AccessKeyID
     * @param accessKeySecret AccessKeySecret
     * @param roleArn RoleArn
     * @return 加密后的配置数组 [encryptedAccessKeyId, encryptedAccessKeySecret, encryptedRoleArn]
     */
    public static String[] encryptAliyunConfigAES(String accessKeyId, String accessKeySecret, String roleArn) {
        log.info("AES encrypting Aliyun configuration - AccessKeyID length: {}, AccessKeySecret length: {}, RoleArn length: {}",
                accessKeyId != null ? accessKeyId.length() : 0,
                accessKeySecret != null ? accessKeySecret.length() : 0,
                roleArn != null ? roleArn.length() : 0);

        String[] encrypted = new String[3];
        encrypted[0] = encryptAES(accessKeyId);
        encrypted[1] = encryptAES(accessKeySecret);
        encrypted[2] = encryptAES(roleArn);

        log.info("Aliyun configuration AES encryption completed");
        return encrypted;
    }

    /**
     * 批量AES解密阿里云配置
     *
     * @param encryptedAccessKeyId 加密的AccessKeyID
     * @param encryptedAccessKeySecret 加密的AccessKeySecret
     * @param encryptedRoleArn 加密的RoleArn
     * @return 解密后的配置数组 [accessKeyId, accessKeySecret, roleArn]
     */
    public static String[] decryptAliyunConfigAES(String encryptedAccessKeyId, String encryptedAccessKeySecret, String encryptedRoleArn) {
        log.info("AES decrypting Aliyun configuration");

        String[] decrypted = new String[3];
        decrypted[0] = decryptAES(encryptedAccessKeyId);
        decrypted[1] = decryptAES(encryptedAccessKeySecret);
        decrypted[2] = decryptAES(encryptedRoleArn);

        log.info("Aliyun configuration AES decryption completed - AccessKeyID length: {}, AccessKeySecret length: {}, RoleArn length: {}",
                decrypted[0] != null ? decrypted[0].length() : 0,
                decrypted[1] != null ? decrypted[1].length() : 0,
                decrypted[2] != null ? decrypted[2].length() : 0);

        return decrypted;
    }

    /**
     * 验证AES加密解密的正确性
     *
     * @param originalText 原始文本
     * @param encryptedText 加密文本
     * @return 是否正确
     */
    public static boolean verifyAES(String originalText, String encryptedText) {
        try {
            String decrypted = decryptAES(encryptedText);
            boolean matches = originalText != null && originalText.equals(decrypted);

            log.debug("AES verification result: {}", matches);
            return matches;

        } catch (Exception e) {
            log.error("AES verification failed", e);
            return false;
        }
    }

    /**
     * 检查是否为有效的AES加密数据
     *
     * @param encryptedData 加密数据
     * @return 是否有效
     */
    public static boolean isValidAESEncryptedData(String encryptedData) {
        if (!StringUtils.hasText(encryptedData)) {
            return false;
        }

        try {
            // 尝试解密来验证数据有效性
            decryptAES(encryptedData);
            return true;
        } catch (Exception e) {
            log.debug("Invalid AES encrypted data: {}", getEncryptedDataSummary(encryptedData));
            return false;
        }
    }
}
