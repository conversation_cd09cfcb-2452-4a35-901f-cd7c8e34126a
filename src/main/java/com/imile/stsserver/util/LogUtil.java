package com.imile.stsserver.util;

import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import java.util.UUID;

/**
 * 日志工具类
 * 提供TraceID管理和日志增强功能
 */
public class LogUtil {

    private static final String TRACE_ID_KEY = "EagleEye-TraceID";
    private static final String USER_ID_KEY = "userId";
    private static final String REQUEST_ID_KEY = "requestId";

    /**
     * 生成并设置TraceID
     * 
     * @return 生成的TraceID
     */
    public static String generateAndSetTraceId() {
        String traceId = generateTraceId();
        setTraceId(traceId);
        return traceId;
    }

    /**
     * 生成TraceID
     * 
     * @return 生成的TraceID
     */
    public static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 设置TraceID到MDC
     * 
     * @param traceId TraceID
     */
    public static void setTraceId(String traceId) {
        if (StringUtils.hasText(traceId)) {
            MDC.put(TRACE_ID_KEY, traceId);
        }
    }

    /**
     * 获取当前TraceID
     * 
     * @return 当前TraceID
     */
    public static String getTraceId() {
        return MDC.get(TRACE_ID_KEY);
    }

    /**
     * 设置用户ID到MDC
     * 
     * @param userId 用户ID
     */
    public static void setUserId(String userId) {
        if (StringUtils.hasText(userId)) {
            MDC.put(USER_ID_KEY, userId);
        }
    }

    /**
     * 获取当前用户ID
     * 
     * @return 当前用户ID
     */
    public static String getUserId() {
        return MDC.get(USER_ID_KEY);
    }

    /**
     * 设置请求ID到MDC
     * 
     * @param requestId 请求ID
     */
    public static void setRequestId(String requestId) {
        if (StringUtils.hasText(requestId)) {
            MDC.put(REQUEST_ID_KEY, requestId);
        }
    }

    /**
     * 获取当前请求ID
     * 
     * @return 当前请求ID
     */
    public static String getRequestId() {
        return MDC.get(REQUEST_ID_KEY);
    }

    /**
     * 清除MDC中的所有数据
     */
    public static void clearMDC() {
        MDC.clear();
    }

    /**
     * 清除TraceID
     */
    public static void clearTraceId() {
        MDC.remove(TRACE_ID_KEY);
    }

    /**
     * 清除用户ID
     */
    public static void clearUserId() {
        MDC.remove(USER_ID_KEY);
    }

    /**
     * 清除请求ID
     */
    public static void clearRequestId() {
        MDC.remove(REQUEST_ID_KEY);
    }

    /**
     * 在指定的代码块中执行，并自动管理TraceID
     * 
     * @param runnable 要执行的代码块
     */
    public static void withTraceId(Runnable runnable) {
        String traceId = generateAndSetTraceId();
        try {
            runnable.run();
        } finally {
            clearTraceId();
        }
    }

    /**
     * 在指定的代码块中执行，并自动管理指定的TraceID
     * 
     * @param traceId 指定的TraceID
     * @param runnable 要执行的代码块
     */
    public static void withTraceId(String traceId, Runnable runnable) {
        String originalTraceId = getTraceId();
        setTraceId(traceId);
        try {
            runnable.run();
        } finally {
            if (originalTraceId != null) {
                setTraceId(originalTraceId);
            } else {
                clearTraceId();
            }
        }
    }
}
