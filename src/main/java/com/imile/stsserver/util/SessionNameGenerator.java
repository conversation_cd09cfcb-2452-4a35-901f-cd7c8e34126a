package com.imile.stsserver.util;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Session Name 生成器
 * 用于生成随机的角色会话名称
 */
public class SessionNameGenerator {

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();
    private static final int DEFAULT_LENGTH = 8;

    /**
     * 生成默认长度的随机 session name
     * 格式：sts-{随机字符串}-{时间戳}
     * 
     * @return 生成的 session name
     */
    public static String generate() {
        return generate(DEFAULT_LENGTH);
    }

    /**
     * 生成指定长度的随机 session name
     * 格式：sts-{随机字符串}-{时间戳}
     * 
     * @param length 随机字符串的长度
     * @return 生成的 session name
     */
    public static String generate(int length) {
        String randomString = generateRandomString(length);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return String.format("sts-%s-%s", randomString, timestamp);
    }

    /**
     * 生成简单的随机字符串（只包含字母和数字）
     * 
     * @param length 字符串长度
     * @return 随机字符串
     */
    public static String generateSimple(int length) {
        return generateRandomString(length);
    }

    /**
     * 生成带前缀的 session name
     * 
     * @param prefix 前缀
     * @param length 随机部分的长度
     * @return 生成的 session name
     */
    public static String generateWithPrefix(String prefix, int length) {
        String randomString = generateRandomString(length);
        return String.format("%s-%s", prefix, randomString);
    }

    /**
     * 生成随机字符串
     * 
     * @param length 字符串长度
     * @return 随机字符串
     */
    private static String generateRandomString(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Length must be positive");
        }

        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = RANDOM.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(index));
        }
        return sb.toString();
    }

    /**
     * 验证 session name 是否符合阿里云规范
     * - 长度在 2-64 个字符之间
     * - 只能包含字母、数字、下划线和连字符
     * 
     * @param sessionName session name
     * @return 是否有效
     */
    public static boolean isValidSessionName(String sessionName) {
        if (sessionName == null || sessionName.trim().isEmpty()) {
            return false;
        }

        String trimmed = sessionName.trim();
        
        // 检查长度
        if (trimmed.length() < 2 || trimmed.length() > 64) {
            return false;
        }

        // 检查字符（只允许字母、数字、下划线和连字符）
        return trimmed.matches("^[a-zA-Z0-9_-]+$");
    }
}
