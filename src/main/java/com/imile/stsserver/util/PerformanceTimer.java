package com.imile.stsserver.util;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 性能计时器工具类
 * 用于统计接口各个环节的耗时
 */
@Slf4j
public class PerformanceTimer {
    
    private final String requestId;
    private final long startTime;
    private final Map<String, Long> stepTimes;
    private final Map<String, Long> stepDurations;
    
    public PerformanceTimer(String requestId) {
        this.requestId = requestId;
        this.startTime = System.currentTimeMillis();
        this.stepTimes = new ConcurrentHashMap<>();
        this.stepDurations = new ConcurrentHashMap<>();
        this.stepTimes.put("start", startTime);
    }
    
    /**
     * 记录步骤开始时间
     */
    public void startStep(String stepName) {
        long currentTime = System.currentTimeMillis();
        stepTimes.put(stepName + "_start", currentTime);
        log.debug("Step started: {} - RequestId: {}", stepName, requestId);
    }
    
    /**
     * 记录步骤结束时间并计算耗时
     */
    public long endStep(String stepName) {
        long currentTime = System.currentTimeMillis();
        stepTimes.put(stepName + "_end", currentTime);
        
        Long startTime = stepTimes.get(stepName + "_start");
        if (startTime != null) {
            long duration = currentTime - startTime;
            stepDurations.put(stepName, duration);
            log.info("Step completed: {} - Duration: {}ms - RequestId: {}", stepName, duration, requestId);
            return duration;
        } else {
            log.warn("Step start time not found for: {} - RequestId: {}", stepName, requestId);
            return 0;
        }
    }
    
    /**
     * 记录步骤耗时（一次性记录）
     */
    public void recordStep(String stepName, long duration) {
        stepDurations.put(stepName, duration);
        log.info("Step recorded: {} - Duration: {}ms - RequestId: {}", stepName, duration, requestId);
    }
    
    /**
     * 获取步骤耗时
     */
    public long getStepDuration(String stepName) {
        return stepDurations.getOrDefault(stepName, 0L);
    }
    
    /**
     * 获取总耗时
     */
    public long getTotalDuration() {
        return System.currentTimeMillis() - startTime;
    }
    
    /**
     * 获取所有步骤的耗时统计
     */
    public Map<String, Long> getAllStepDurations() {
        return new HashMap<>(stepDurations);
    }
    
    /**
     * 打印性能摘要
     */
    public void printSummary() {
        long totalTime = getTotalDuration();
        
        log.info("=== Performance Summary - RequestId: {} ===", requestId);
        log.info("Total Duration: {}ms", totalTime);
        
        if (!stepDurations.isEmpty()) {
            log.info("Step Breakdown:");
            stepDurations.entrySet().stream()
                    .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                    .forEach(entry -> {
                        double percentage = (double) entry.getValue() / totalTime * 100;
                        log.info("  {}: {}ms ({:.1f}%)", entry.getKey(), entry.getValue(), percentage);
                    });
        }
        
        log.info("=== Performance Summary End - RequestId: {} ===", requestId);
    }
    
    /**
     * 打印详细的性能报告
     */
    public void printDetailedReport() {
        long totalTime = getTotalDuration();
        
        log.info("=== Detailed Performance Report - RequestId: {} ===", requestId);
        log.info("Request Start Time: {}", startTime);
        log.info("Request End Time: {}", System.currentTimeMillis());
        log.info("Total Duration: {}ms", totalTime);
        
        if (!stepDurations.isEmpty()) {
            log.info("Detailed Step Analysis:");
            
            long accumulatedTime = 0;
            for (Map.Entry<String, Long> entry : stepDurations.entrySet()) {
                String stepName = entry.getKey();
                long duration = entry.getValue();
                accumulatedTime += duration;
                double percentage = (double) duration / totalTime * 100;
                double accumulatedPercentage = (double) accumulatedTime / totalTime * 100;
                
                log.info("  Step: {} | Duration: {}ms | Percentage: {:.1f}% | Accumulated: {:.1f}%", 
                        stepName, duration, percentage, accumulatedPercentage);
            }
            
            // 计算未统计的时间
            long unaccountedTime = totalTime - accumulatedTime;
            if (unaccountedTime > 0) {
                double unaccountedPercentage = (double) unaccountedTime / totalTime * 100;
                log.info("  Unaccounted Time: {}ms ({:.1f}%)", unaccountedTime, unaccountedPercentage);
            }
        }
        
        // 性能评估
        if (totalTime <= 100) {
            log.info("Performance Rating: EXCELLENT (≤100ms)");
        } else if (totalTime <= 500) {
            log.info("Performance Rating: GOOD (≤500ms)");
        } else if (totalTime <= 1000) {
            log.info("Performance Rating: ACCEPTABLE (≤1000ms)");
        } else {
            log.info("Performance Rating: NEEDS_IMPROVEMENT (>1000ms)");
        }
        
        log.info("=== Detailed Performance Report End - RequestId: {} ===", requestId);
    }
    
    /**
     * 获取性能数据用于响应头
     */
    public Map<String, String> getPerformanceHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Total-Time", String.valueOf(getTotalDuration()));
        headers.put("X-Request-Id", requestId);
        
        // 添加主要步骤的耗时
        stepDurations.forEach((step, duration) -> {
            String headerName = "X-" + step.replace("_", "-").replace(" ", "-") + "-Time";
            headers.put(headerName, String.valueOf(duration));
        });
        
        return headers;
    }
    
    /**
     * 创建性能统计的JSON字符串
     */
    public String toJsonString() {
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"requestId\":\"").append(requestId).append("\",");
        json.append("\"totalDuration\":").append(getTotalDuration()).append(",");
        json.append("\"steps\":{");
        
        boolean first = true;
        for (Map.Entry<String, Long> entry : stepDurations.entrySet()) {
            if (!first) {
                json.append(",");
            }
            json.append("\"").append(entry.getKey()).append("\":").append(entry.getValue());
            first = false;
        }
        
        json.append("}}");
        return json.toString();
    }
    
    /**
     * 静态方法：创建新的计时器
     */
    public static PerformanceTimer start(String requestId) {
        return new PerformanceTimer(requestId);
    }
    
    /**
     * 静态方法：创建带自动生成ID的计时器
     */
    public static PerformanceTimer start() {
        String requestId = "REQ-" + System.currentTimeMillis() + "-" + Thread.currentThread().getId();
        return new PerformanceTimer(requestId);
    }
}
