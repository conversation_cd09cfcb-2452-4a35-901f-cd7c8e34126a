package com.imile.stsserver.controller;

import com.imile.stsserver.exception.StsException;
import com.imile.stsserver.model.sts.AssumeRoleRequest;
import com.imile.stsserver.model.sts.ProtocolType;
import com.imile.stsserver.model.sts.StsTokenResponse;
import com.imile.stsserver.service.AppTokenServerService;
import com.imile.stsserver.service.StsTokenCacheService;
import com.imile.stsserver.service.AliyunAkService;
import com.imile.stsserver.entity.AliyunAk;
import com.imile.stsserver.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.scheduling.annotation.Async;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * App Token Server Controller
 * 只提供 STS Token 接口
 */
@Slf4j
@RestController
@RequestMapping("/api/sts")
@Validated
public class AppTokenServerController {

    private final AppTokenServerService appTokenServerService;
    private final StsTokenCacheService cacheService;
    private final AliyunAkService aliyunAkService;

    // 性能监控
    private final AtomicLong requestCounter = new AtomicLong(0);
    private final AtomicLong cacheHitCounter = new AtomicLong(0);
    private final AtomicLong cacheMissCounter = new AtomicLong(0);
    private final AtomicLong errorCounter = new AtomicLong(0);

    // 配置缓存，避免频繁数据库查询
    private final ConcurrentHashMap<String, AliyunAk> configCache = new ConcurrentHashMap<>();
    private volatile long lastConfigCacheTime = 0;
    private static final long CONFIG_CACHE_TTL = 60000; // 1分钟缓存

    public AppTokenServerController(AppTokenServerService appTokenServerService,
                                   StsTokenCacheService cacheService,
                                   AliyunAkService aliyunAkService) {
        this.appTokenServerService = appTokenServerService;
        this.cacheService = cacheService;
        this.aliyunAkService = aliyunAkService;
    }



    /**
     * 高性能获取配置（带缓存）
     * 避免每次请求都查询数据库
     */
    private AliyunAk getConfigWithCache(String token) {
        long currentTime = System.currentTimeMillis();

        // 检查缓存是否过期
        if (currentTime - lastConfigCacheTime > CONFIG_CACHE_TTL) {
            configCache.clear();
            lastConfigCacheTime = currentTime;
        }

        // 从缓存获取
        AliyunAk config = configCache.get(token);
        if (config != null) {
            return config;
        }

        // 缓存未命中，从数据库获取
        try {
            config = aliyunAkService.getByTokenAndEnabled(token);
            if (config != null) {
                configCache.put(token, config);
            }
            return config;
        } catch (Exception e) {
            log.error("Failed to get config from database for token: {}",
                    token != null ? token.substring(0, Math.min(8, token.length())) + "***" : "null", e);
            return null;
        }
    }

    /**
     * 异步记录性能指标
     */
    @Async
    private void recordMetrics(boolean cacheHit, boolean hasError) {
        requestCounter.incrementAndGet();
        if (cacheHit) {
            cacheHitCounter.incrementAndGet();
        } else {
            cacheMissCounter.incrementAndGet();
        }
        if (hasError) {
            errorCounter.incrementAndGet();
        }
    }

    /**
     * 创建响应头（包含性能信息）
     */
    private HttpHeaders createResponseHeaders(String cacheStatus, String cacheKey, long remainingTime) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-Cache-Status", cacheStatus);
        headers.add("X-Cache-Key", cacheKey);
        headers.add("X-Token-Remaining-Time", String.valueOf(remainingTime));
        headers.add("X-Response-Time", String.valueOf(System.currentTimeMillis()));
        headers.add("X-Server-Name", "AppTokenServer");
        headers.add("X-API-Version", "v1.0");
        return headers;
    }

    /**
     * 创建错误响应
     */
    private ResponseEntity<StsTokenResponse> createErrorResponse(String statusCode, String errorCode,
                                                               String errorMessage, HttpStatus httpStatus) {
        StsTokenResponse errorResponse = new StsTokenResponse();
        errorResponse.setStatusCode(statusCode);
        errorResponse.setErrorCode(errorCode);
        errorResponse.setErrorMessage(errorMessage);
        return ResponseEntity.status(httpStatus).body(errorResponse);
    }

    /**
     * 获取STS Token - 阿里云官方格式
     * 返回与阿里云STS接口相同的JSON格式
     *
     * @param accessKeyId Access Key ID
     * @param accessKeySecret Access Key Secret
     * @param request Assume role request (roleSessionName 可选，为空时自动生成)
     * @param protocolType Protocol type (optional, defaults to HTTPS)
     * @return StsTokenResponse 阿里云官方格式的响应
     */
    @PostMapping("/token")
    public CompletableFuture<ResponseEntity<StsTokenResponse>> getStsToken(
            @Valid @RequestBody AssumeRoleRequest request,
            @RequestParam(value = "protocol", defaultValue = "HTTPS") String protocolType,
            HttpServletRequest httpRequest) {

        return CompletableFuture.supplyAsync(() -> {
            return processStsTokenRequest(request, protocolType, httpRequest);
        });
    }

    /**
     * 处理STS Token请求的核心逻辑（优化版）
     */
    private ResponseEntity<StsTokenResponse> processStsTokenRequest(
            AssumeRoleRequest request, String protocolType, HttpServletRequest httpRequest) {

        long startTime = System.currentTimeMillis();
        boolean cacheHit = false;
        boolean hasError = false;
        String requestId = "REQ-" + System.currentTimeMillis() + "-" + Thread.currentThread().getId();

        try {
            // 获取客户端IP（优化：减少字符串操作）
            String clientIp = IpUtil.getClientIp(httpRequest);
            String formattedIp = IpUtil.formatIpForLog(clientIp);

            // 记录请求开始
            log.info("=== STS Token Request Started - RequestId: {} ===", requestId);
            log.info("Request Info - IP: {}, Token: {}, RoleArn: {}, SessionName: {}, Duration: {}s",
                    formattedIp,
                    request.getToken() != null ? request.getToken().substring(0, Math.min(8, request.getToken().length())) + "***" : "null",
                    request.getRoleArn(),
                    request.getRoleSessionName(),
                    request.getDurationSeconds() != null ? request.getDurationSeconds() : 3600L);

            // 高性能配置获取（带缓存）
            long configStartTime = System.currentTimeMillis();
            AliyunAk aliyunConfig = getConfigWithCache(request.getToken());
            long configEndTime = System.currentTimeMillis();
            log.info("Config retrieval time: {}ms - RequestId: {}", (configEndTime - configStartTime), requestId);

            if (aliyunConfig == null) {
                hasError = true;
                log.error("Authentication failed - RequestId: {}, Token: {}", requestId,
                        request.getToken() != null ? request.getToken().substring(0, Math.min(8, request.getToken().length())) + "***" : "null");
                return createErrorResponse("400", "AUTHENTICATION_FAILED",
                        "Invalid authentication token or configuration not found", HttpStatus.BAD_REQUEST);
            }

            // 使用数据库中的RoleArn，如果请求中没有提供的话
            String actualRoleArn = request.getRoleArn() != null ? request.getRoleArn() : aliyunConfig.getRoleArn();

            // 生成缓存键（优化：减少字符串拼接）
            String cacheKey = cacheService.generateCacheKey(
                    clientIp + "_" + request.getToken(),
                    actualRoleArn,
                    request.getRoleSessionName(),
                    request.getPolicy(),
                    request.getDurationSeconds()
            );

            // 尝试从缓存获取（优化：快速缓存检查）
            long cacheStartTime = System.currentTimeMillis();
            StsTokenResponse cachedResponse = cacheService.getFromCache(cacheKey);
            long cacheEndTime = System.currentTimeMillis();
            log.info("Cache check time: {}ms - RequestId: {}", (cacheEndTime - cacheStartTime), requestId);

            if (cachedResponse != null) {
                cacheHit = true;

                // 为缓存的响应设置请求开始时间
                java.time.Instant requestStartInstant = java.time.Instant.ofEpochMilli(startTime);
                java.time.LocalDateTime startDateTime = java.time.LocalDateTime.ofInstant(requestStartInstant, java.time.ZoneId.systemDefault());
                String isoStartTime = startDateTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
                cachedResponse.setRequestStartTime(isoStartTime);
                // 设置请求开始时间戳（秒，与ExpirationTimestamp保持一致）
                cachedResponse.setRequestStartTimestamp(requestStartInstant.getEpochSecond());

                long remainingTime = cacheService.getTokenRemainingTime(cachedResponse);
                HttpHeaders headers = createResponseHeaders("HIT", cacheKey, remainingTime);
                headers.add("X-Request-Id", requestId);

                long totalTime = System.currentTimeMillis() - startTime;
                log.info("=== Cache Hit - Request Completed - RequestId: {}, Total Time: {}ms ===", requestId, totalTime);

                return ResponseEntity.ok().headers(headers).body(cachedResponse);
            }

            // 缓存未命中，生成新token
            log.info("Cache miss - Generating new token - RequestId: {}", requestId);
            return generateNewToken(request, protocolType, aliyunConfig, actualRoleArn, cacheKey, formattedIp, requestId, startTime);

        } catch (Exception e) {
            hasError = true;
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("=== STS Token Request Failed - RequestId: {}, Total Time: {}ms ===", requestId, totalTime, e);
            return createErrorResponse("500", "INTERNAL_ERROR", "Internal server error occurred", HttpStatus.INTERNAL_SERVER_ERROR);
        } finally {
            // 异步记录性能指标
            recordMetrics(cacheHit, hasError);
        }
    }

    /**
     * 生成新的STS Token（带详细耗时统计）
     */
    private ResponseEntity<StsTokenResponse> generateNewToken(AssumeRoleRequest request, String protocolType,
                                                            AliyunAk aliyunConfig, String actualRoleArn,
                                                            String cacheKey, String formattedIp,
                                                            String requestId, long requestStartTime) {
        try {
            // 验证协议类型
            long protocolStartTime = System.currentTimeMillis();
            ProtocolType protocol = ProtocolType.valueOf(protocolType.toUpperCase());
            long protocolEndTime = System.currentTimeMillis();
            log.info("Protocol validation time: {}ms - RequestId: {}", (protocolEndTime - protocolStartTime), requestId);

            // 调用STS服务生成token
            long stsStartTime = System.currentTimeMillis();
            log.info("Calling STS service - RequestId: {}", requestId);

            // 将请求开始时间转换为Instant
            java.time.Instant requestStartInstant = java.time.Instant.ofEpochMilli(requestStartTime);

            StsTokenResponse tokenResponse = appTokenServerService.getStsToken(
                    aliyunConfig.getAccessKeyId(),
                    aliyunConfig.getAccessKeySecret(),
                    actualRoleArn,
                    request.getRoleSessionName(),
                    request.getPolicy(),
                    protocol,
                    request.getDurationSeconds() != null ? request.getDurationSeconds() : 3600L,
                    requestStartInstant
            );

            long stsEndTime = System.currentTimeMillis();
            long stsCallTime = stsEndTime - stsStartTime;
            log.info("STS service call time: {}ms - RequestId: {}", stsCallTime, requestId);
            log.info("STS token generated successfully - StatusCode: {}, AccessKeyId: {}, Expiration: {} - RequestId: {}",
                    tokenResponse.getStatusCode(),
                    tokenResponse.getAccessKeyId() != null ? tokenResponse.getAccessKeyId().substring(0, Math.min(8, tokenResponse.getAccessKeyId().length())) + "***" : "null",
                    tokenResponse.getExpiration(),
                    requestId);

            // 异步缓存新token
            long cacheStartTime = System.currentTimeMillis();
            CompletableFuture.runAsync(() -> {
                try {
                    long asyncCacheStart = System.currentTimeMillis();
                    cacheService.putToCache(cacheKey, tokenResponse);
                    long asyncCacheEnd = System.currentTimeMillis();
                    log.info("Async cache put time: {}ms - RequestId: {}", (asyncCacheEnd - asyncCacheStart), requestId);
                } catch (Exception e) {
                    log.warn("Failed to cache token asynchronously - RequestId: {}", requestId, e);
                }
            });
            long cacheEndTime = System.currentTimeMillis();
            log.info("Cache async setup time: {}ms - RequestId: {}", (cacheEndTime - cacheStartTime), requestId);

            // 创建响应头
            long headerStartTime = System.currentTimeMillis();
            long remainingTime = cacheService.getTokenRemainingTime(tokenResponse);
            HttpHeaders headers = createResponseHeaders("MISS", cacheKey, remainingTime);
            headers.add("X-Request-Id", requestId);
            headers.add("X-STS-Call-Time", String.valueOf(stsCallTime));
            long headerEndTime = System.currentTimeMillis();
            log.info("Response header creation time: {}ms - RequestId: {}", (headerEndTime - headerStartTime), requestId);

            // 计算总耗时
            long totalTime = System.currentTimeMillis() - requestStartTime;
            log.info("=== STS Token Request Completed Successfully - RequestId: {}, Total Time: {}ms ===", requestId, totalTime);
            log.info("Time breakdown - Config: {}ms, Cache Check: {}ms, STS Call: {}ms, Total: {}ms - RequestId: {}",
                    "N/A", "N/A", stsCallTime, totalTime, requestId);

            return ResponseEntity.ok().headers(headers).body(tokenResponse);

        } catch (IllegalArgumentException e) {
            long totalTime = System.currentTimeMillis() - requestStartTime;
            log.error("=== Protocol Validation Failed - RequestId: {}, Total Time: {}ms ===", requestId, totalTime, e);
            return createErrorResponse("400", "INVALID_PROTOCOL",
                    "Invalid protocol type: " + protocolType, HttpStatus.BAD_REQUEST);
        } catch (StsException e) {
            long totalTime = System.currentTimeMillis() - requestStartTime;
            log.error("=== STS Service Error - RequestId: {}, Total Time: {}ms, ErrorCode: {}, Message: {} ===",
                    requestId, totalTime, e.getErrorCode(), e.getMessage(), e);
            return createErrorResponse("500", e.getErrorCode(), e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - requestStartTime;
            log.error("=== Unexpected Error - RequestId: {}, Total Time: {}ms ===", requestId, totalTime, e);
            return createErrorResponse("500", "INTERNAL_ERROR",
                    "Internal server error occurred", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
