package com.imile.stsserver.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 性能监控Controller
 * 提供系统性能指标和监控信息
 */
@Slf4j
@RestController
@RequestMapping("/api/monitor")
public class PerformanceMonitorController {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取系统性能指标
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> getMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        try {
            // JVM指标
            Runtime runtime = Runtime.getRuntime();
            metrics.put("jvm", Map.of(
                "totalMemory", runtime.totalMemory(),
                "freeMemory", runtime.freeMemory(),
                "usedMemory", runtime.totalMemory() - runtime.freeMemory(),
                "maxMemory", runtime.maxMemory(),
                "availableProcessors", runtime.availableProcessors()
            ));
            
            // 线程指标
            ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
            metrics.put("threads", Map.of(
                "threadCount", threadBean.getThreadCount(),
                "peakThreadCount", threadBean.getPeakThreadCount(),
                "daemonThreadCount", threadBean.getDaemonThreadCount(),
                "totalStartedThreadCount", threadBean.getTotalStartedThreadCount()
            ));
            
            // 数据库连接池指标
            if (dataSource instanceof com.zaxxer.hikari.HikariDataSource) {
                com.zaxxer.hikari.HikariDataSource hikariDS = (com.zaxxer.hikari.HikariDataSource) dataSource;
                com.zaxxer.hikari.HikariPoolMXBean poolBean = hikariDS.getHikariPoolMXBean();
                
                metrics.put("database", Map.of(
                    "activeConnections", poolBean.getActiveConnections(),
                    "idleConnections", poolBean.getIdleConnections(),
                    "totalConnections", poolBean.getTotalConnections(),
                    "threadsAwaitingConnection", poolBean.getThreadsAwaitingConnection(),
                    "maximumPoolSize", hikariDS.getMaximumPoolSize(),
                    "minimumIdle", hikariDS.getMinimumIdle()
                ));
            }
            
            // Redis连接指标
            try {
                String redisPing = redisTemplate.getConnectionFactory().getConnection().ping();
                metrics.put("redis", Map.of(
                    "status", "connected",
                    "ping", redisPing,
                    "connectionFactory", redisTemplate.getConnectionFactory().getClass().getSimpleName()
                ));
            } catch (Exception e) {
                metrics.put("redis", Map.of(
                    "status", "error",
                    "error", e.getMessage()
                ));
            }
            
            // 系统负载
            com.sun.management.OperatingSystemMXBean osBean = 
                (com.sun.management.OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
            
            metrics.put("system", Map.of(
                "processCpuLoad", osBean.getProcessCpuLoad(),
                "systemCpuLoad", osBean.getSystemCpuLoad(),
                "committedVirtualMemorySize", osBean.getCommittedVirtualMemorySize(),
                "freePhysicalMemorySize", osBean.getFreePhysicalMemorySize(),
                "totalPhysicalMemorySize", osBean.getTotalPhysicalMemorySize()
            ));
            
            metrics.put("timestamp", System.currentTimeMillis());
            metrics.put("success", true);
            
            return ResponseEntity.ok(metrics);
            
        } catch (Exception e) {
            log.error("Failed to get performance metrics", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取STS服务特定指标
     */
    @GetMapping("/sts-metrics")
    public ResponseEntity<Map<String, Object>> getStsMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        try {
            // 这里可以添加STS服务特定的指标
            // 例如：请求计数、缓存命中率、响应时间等
            
            // 缓存统计
            try {
                // 获取Redis中STS相关的键数量
                var keys = redisTemplate.keys("sts:token:*");
                metrics.put("cache", Map.of(
                    "stsTokenCount", keys != null ? keys.size() : 0,
                    "cacheType", "Redis"
                ));
            } catch (Exception e) {
                metrics.put("cache", Map.of(
                    "error", e.getMessage()
                ));
            }
            
            // 数据库连接测试
            try (Connection conn = dataSource.getConnection()) {
                boolean isValid = conn.isValid(5);
                metrics.put("database", Map.of(
                    "connectionValid", isValid,
                    "autoCommit", conn.getAutoCommit(),
                    "readOnly", conn.isReadOnly()
                ));
            } catch (Exception e) {
                metrics.put("database", Map.of(
                    "error", e.getMessage()
                ));
            }
            
            metrics.put("timestamp", System.currentTimeMillis());
            metrics.put("success", true);
            
            return ResponseEntity.ok(metrics);
            
        } catch (Exception e) {
            log.error("Failed to get STS metrics", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        boolean allHealthy = true;
        
        try {
            // 检查数据库
            try (Connection conn = dataSource.getConnection()) {
                boolean dbHealthy = conn.isValid(5);
                health.put("database", Map.of(
                    "status", dbHealthy ? "UP" : "DOWN",
                    "details", dbHealthy ? "Connection valid" : "Connection invalid"
                ));
                allHealthy &= dbHealthy;
            } catch (Exception e) {
                health.put("database", Map.of(
                    "status", "DOWN",
                    "error", e.getMessage()
                ));
                allHealthy = false;
            }
            
            // 检查Redis
            try {
                String ping = redisTemplate.getConnectionFactory().getConnection().ping();
                boolean redisHealthy = "PONG".equals(ping);
                health.put("redis", Map.of(
                    "status", redisHealthy ? "UP" : "DOWN",
                    "ping", ping
                ));
                allHealthy &= redisHealthy;
            } catch (Exception e) {
                health.put("redis", Map.of(
                    "status", "DOWN",
                    "error", e.getMessage()
                ));
                allHealthy = false;
            }
            
            // 检查内存使用率
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            long maxMemory = runtime.maxMemory();
            double memoryUsage = (double) usedMemory / maxMemory;
            
            boolean memoryHealthy = memoryUsage < 0.9; // 内存使用率低于90%
            health.put("memory", Map.of(
                "status", memoryHealthy ? "UP" : "WARNING",
                "usage", String.format("%.2f%%", memoryUsage * 100),
                "used", usedMemory,
                "max", maxMemory
            ));
            
            if (!memoryHealthy) {
                allHealthy = false;
            }
            
            health.put("overall", allHealthy ? "UP" : "DOWN");
            health.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            log.error("Health check failed", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("overall", "DOWN");
            errorResponse.put("error", e.getMessage());
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取线程池状态
     */
    @GetMapping("/thread-pools")
    public ResponseEntity<Map<String, Object>> getThreadPoolStatus() {
        Map<String, Object> threadPools = new HashMap<>();
        
        try {
            // 这里可以添加自定义线程池的监控
            // 由于我们使用了Spring的@Async，可以通过JMX或其他方式获取线程池状态
            
            ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
            threadPools.put("system", Map.of(
                "threadCount", threadBean.getThreadCount(),
                "peakThreadCount", threadBean.getPeakThreadCount(),
                "daemonThreadCount", threadBean.getDaemonThreadCount()
            ));
            
            threadPools.put("timestamp", System.currentTimeMillis());
            threadPools.put("success", true);
            
            return ResponseEntity.ok(threadPools);
            
        } catch (Exception e) {
            log.error("Failed to get thread pool status", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
