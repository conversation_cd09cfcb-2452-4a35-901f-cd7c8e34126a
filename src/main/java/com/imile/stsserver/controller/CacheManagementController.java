package com.imile.stsserver.controller;

import com.imile.stsserver.service.CacheManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 缓存管理Controller
 * 提供缓存监控、清理等管理功能
 */
@Slf4j
@RestController
@RequestMapping("/api/cache")
public class CacheManagementController {

    private final CacheManagementService cacheManagementService;

    public CacheManagementController(CacheManagementService cacheManagementService) {
        this.cacheManagementService = cacheManagementService;
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getCacheStats() {
        log.info("=== Cache Stats Request Started ===");
        
        try {
            Map<String, Object> stats = cacheManagementService.getCacheStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            
            log.info("Cache stats generated successfully");
            log.info("=== Cache Stats Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("=== Cache Stats Request Failed ===", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取缓存健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getCacheHealth() {
        log.info("=== Cache Health Check Request Started ===");
        
        try {
            Map<String, Object> health = cacheManagementService.getCacheHealth();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", health);
            
            log.info("Cache health check completed");
            log.info("=== Cache Health Check Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("=== Cache Health Check Request Failed ===", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 清除指定缓存
     */
    @DeleteMapping("/{cacheName}")
    public ResponseEntity<Map<String, Object>> clearCache(@PathVariable String cacheName) {
        log.info("=== Clear Cache Request Started - Cache: {} ===", cacheName);
        
        try {
            boolean success = cacheManagementService.clearCache(cacheName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? 
                "Cache cleared successfully" : "Failed to clear cache or cache not found");
            response.put("cacheName", cacheName);
            
            if (success) {
                log.info("Successfully cleared cache: {}", cacheName);
                log.info("=== Clear Cache Request Completed ===");
                return ResponseEntity.ok(response);
            } else {
                log.warn("Failed to clear cache: {}", cacheName);
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            log.error("=== Clear Cache Request Failed - Cache: {} ===", cacheName, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 清除所有缓存
     */
    @DeleteMapping("/all")
    public ResponseEntity<Map<String, Object>> clearAllCaches() {
        log.info("=== Clear All Caches Request Started ===");
        
        try {
            boolean success = cacheManagementService.clearAllCaches();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? 
                "All caches cleared successfully" : "Failed to clear all caches");
            
            if (success) {
                log.info("Successfully cleared all caches");
                log.info("=== Clear All Caches Request Completed ===");
                return ResponseEntity.ok(response);
            } else {
                log.warn("Failed to clear all caches");
                return ResponseEntity.internalServerError().body(response);
            }
            
        } catch (Exception e) {
            log.error("=== Clear All Caches Request Failed ===", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 清除策略缓存
     */
    @DeleteMapping("/policy")
    public ResponseEntity<Map<String, Object>> clearPolicyCache() {
        log.info("=== Clear Policy Cache Request Started ===");
        
        try {
            boolean success = cacheManagementService.clearPolicyCache();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? 
                "Policy cache cleared successfully" : "Failed to clear policy cache");
            
            if (success) {
                log.info("Successfully cleared policy cache");
                log.info("=== Clear Policy Cache Request Completed ===");
                return ResponseEntity.ok(response);
            } else {
                log.warn("Failed to clear policy cache");
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            log.error("=== Clear Policy Cache Request Failed ===", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 清除指定策略名称的缓存
     */
    @DeleteMapping("/policy/name/{policyName}")
    public ResponseEntity<Map<String, Object>> clearPolicyCacheByName(@PathVariable String policyName) {
        log.info("=== Clear Policy Cache By Name Request Started - Name: {} ===", policyName);
        
        try {
            boolean success = cacheManagementService.clearPolicyCacheByName(policyName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? 
                "Policy cache cleared successfully" : "Failed to clear policy cache");
            response.put("policyName", policyName);
            
            if (success) {
                log.info("Successfully cleared policy cache for name: {}", policyName);
                log.info("=== Clear Policy Cache By Name Request Completed ===");
                return ResponseEntity.ok(response);
            } else {
                log.warn("Failed to clear policy cache for name: {}", policyName);
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            log.error("=== Clear Policy Cache By Name Request Failed - Name: {} ===", policyName, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 预热策略缓存
     */
    @PostMapping("/policy/warmup")
    public ResponseEntity<Map<String, Object>> warmupPolicyCache() {
        log.info("=== Warmup Policy Cache Request Started ===");
        
        try {
            boolean success = cacheManagementService.warmupPolicyCache();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? 
                "Policy cache warmed up successfully" : "Failed to warmup policy cache");
            
            if (success) {
                log.info("Successfully warmed up policy cache");
                log.info("=== Warmup Policy Cache Request Completed ===");
                return ResponseEntity.ok(response);
            } else {
                log.warn("Failed to warmup policy cache");
                return ResponseEntity.internalServerError().body(response);
            }
            
        } catch (Exception e) {
            log.error("=== Warmup Policy Cache Request Failed ===", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
