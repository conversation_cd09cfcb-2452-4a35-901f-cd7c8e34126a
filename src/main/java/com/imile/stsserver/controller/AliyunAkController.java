package com.imile.stsserver.controller;

import com.imile.stsserver.entity.AliyunAk;
import com.imile.stsserver.service.AliyunAkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 阿里云AccessKey配置管理Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/aliyun/ak")
public class AliyunAkController {

    private final AliyunAkService aliyunAkService;

    public AliyunAkController(AliyunAkService aliyunAkService) {
        this.aliyunAkService = aliyunAkService;
    }

    /**
     * 获取所有配置
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> listConfigs() {
        log.info("=== Aliyun AK List Request Started ===");
        
        try {
            List<AliyunAk> configs = aliyunAkService.getAll();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", configs);
            response.put("total", configs.size());
            response.put("enabledCount", aliyunAkService.getEnabledCount());
            
            log.info("Successfully retrieved {} Aliyun AK configurations", configs.size());
            log.info("=== Aliyun AK List Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("=== Aliyun AK List Request Failed ===");
            log.error("Failed to list Aliyun AK configurations", e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to list configurations: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取启用的配置
     */
    @GetMapping("/enabled")
    public ResponseEntity<Map<String, Object>> listEnabledConfigs() {
        log.info("=== Enabled Aliyun AK List Request Started ===");
        
        try {
            List<AliyunAk> configs = aliyunAkService.getAllEnabled();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", configs);
            response.put("total", configs.size());
            
            log.info("Successfully retrieved {} enabled Aliyun AK configurations", configs.size());
            log.info("=== Enabled Aliyun AK List Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("=== Enabled Aliyun AK List Request Failed ===");
            log.error("Failed to list enabled Aliyun AK configurations", e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to list enabled configurations: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 根据ID获取配置
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getConfig(@PathVariable Long id) {
        log.info("=== Aliyun AK Get Request Started - ID: {} ===", id);
        
        try {
            AliyunAk config = aliyunAkService.getById(id);
            
            if (config == null) {
                log.warn("Aliyun AK configuration not found - ID: {}", id);
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("error", "Configuration not found");
                return ResponseEntity.notFound().build();
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", config);
            
            log.info("Successfully retrieved Aliyun AK configuration - ID: {}", id);
            log.info("=== Aliyun AK Get Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("=== Aliyun AK Get Request Failed ===");
            log.error("Failed to get Aliyun AK configuration - ID: {}", id, e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to get configuration: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 创建新配置
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createConfig(@Valid @RequestBody AliyunAk aliyunAk) {
        log.info("=== Aliyun AK Create Request Started ===");
        log.info("Creating configuration - AccessKeyID: {}, RoleArn: {}", 
                aliyunAk.getMaskedAccessKeyId(), aliyunAk.getRoleArn());
        
        try {
            AliyunAk created = aliyunAkService.create(aliyunAk);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", created);
            response.put("message", "Configuration created successfully");
            
            log.info("Successfully created Aliyun AK configuration - ID: {}", created.getId());
            log.info("=== Aliyun AK Create Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            log.error("=== Aliyun AK Create Request Failed - Validation Error ===");
            log.error("Validation error: {}", e.getMessage());
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            
            return ResponseEntity.badRequest().body(errorResponse);
            
        } catch (Exception e) {
            log.error("=== Aliyun AK Create Request Failed ===");
            log.error("Failed to create Aliyun AK configuration", e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to create configuration: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 更新配置
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateConfig(@PathVariable Long id, 
                                                           @Valid @RequestBody AliyunAk aliyunAk) {
        log.info("=== Aliyun AK Update Request Started - ID: {} ===", id);
        
        try {
            aliyunAk.setId(id);
            AliyunAk updated = aliyunAkService.update(aliyunAk);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updated);
            response.put("message", "Configuration updated successfully");
            
            log.info("Successfully updated Aliyun AK configuration - ID: {}", id);
            log.info("=== Aliyun AK Update Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            log.error("=== Aliyun AK Update Request Failed - Validation Error ===");
            log.error("Validation error: {}", e.getMessage());
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            
            return ResponseEntity.badRequest().body(errorResponse);
            
        } catch (Exception e) {
            log.error("=== Aliyun AK Update Request Failed ===");
            log.error("Failed to update Aliyun AK configuration - ID: {}", id, e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to update configuration: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 启用配置
     */
    @PutMapping("/{id}/enable")
    public ResponseEntity<Map<String, Object>> enableConfig(@PathVariable Long id) {
        log.info("=== Aliyun AK Enable Request Started - ID: {} ===", id);
        
        try {
            boolean success = aliyunAkService.enable(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "Configuration enabled successfully" : "Failed to enable configuration");
            
            if (success) {
                log.info("Successfully enabled Aliyun AK configuration - ID: {}", id);
                log.info("=== Aliyun AK Enable Request Completed ===");
                return ResponseEntity.ok(response);
            } else {
                log.error("Failed to enable Aliyun AK configuration - ID: {}", id);
                return ResponseEntity.internalServerError().body(response);
            }
            
        } catch (Exception e) {
            log.error("=== Aliyun AK Enable Request Failed ===");
            log.error("Failed to enable Aliyun AK configuration - ID: {}", id, e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to enable configuration: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 禁用配置
     */
    @PutMapping("/{id}/disable")
    public ResponseEntity<Map<String, Object>> disableConfig(@PathVariable Long id) {
        log.info("=== Aliyun AK Disable Request Started - ID: {} ===", id);
        
        try {
            boolean success = aliyunAkService.disable(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "Configuration disabled successfully" : "Failed to disable configuration");
            
            if (success) {
                log.info("Successfully disabled Aliyun AK configuration - ID: {}", id);
                log.info("=== Aliyun AK Disable Request Completed ===");
                return ResponseEntity.ok(response);
            } else {
                log.error("Failed to disable Aliyun AK configuration - ID: {}", id);
                return ResponseEntity.internalServerError().body(response);
            }
            
        } catch (Exception e) {
            log.error("=== Aliyun AK Disable Request Failed ===");
            log.error("Failed to disable Aliyun AK configuration - ID: {}", id, e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to disable configuration: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 删除配置
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteConfig(@PathVariable Long id) {
        log.info("=== Aliyun AK Delete Request Started - ID: {} ===", id);
        
        try {
            boolean success = aliyunAkService.delete(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "Configuration deleted successfully" : "Configuration not found");
            
            if (success) {
                log.info("Successfully deleted Aliyun AK configuration - ID: {}", id);
                log.info("=== Aliyun AK Delete Request Completed ===");
                return ResponseEntity.ok(response);
            } else {
                log.warn("Aliyun AK configuration not found for deletion - ID: {}", id);
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("=== Aliyun AK Delete Request Failed ===");
            log.error("Failed to delete Aliyun AK configuration - ID: {}", id, e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to delete configuration: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
