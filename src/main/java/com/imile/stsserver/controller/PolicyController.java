package com.imile.stsserver.controller;

import com.imile.stsserver.entity.Policy;
import com.imile.stsserver.service.PolicyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 策略管理Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/policy")
public class PolicyController {

    private final PolicyService policyService;

    public PolicyController(PolicyService policyService) {
        this.policyService = policyService;
    }

    /**
     * 获取所有策略
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> listPolicies() {
        log.info("=== Policy List Request Started ===");
        
        try {
            List<Policy> policies = policyService.getAll();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", policies);
            response.put("total", policies.size());
            response.put("enabledCount", policyService.getEnabledCount());
            
            log.info("Successfully retrieved {} policies", policies.size());
            log.info("=== Policy List Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("=== Policy List Request Failed ===", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取启用的策略
     */
    @GetMapping("/enabled")
    public ResponseEntity<Map<String, Object>> listEnabledPolicies() {
        log.info("=== Enabled Policy List Request Started ===");
        
        try {
            List<Policy> policies = policyService.getAllEnabled();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", policies);
            response.put("total", policies.size());
            
            log.info("Successfully retrieved {} enabled policies", policies.size());
            log.info("=== Enabled Policy List Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("=== Enabled Policy List Request Failed ===", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 根据ID获取策略
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getPolicyById(@PathVariable Long id) {
        log.info("=== Get Policy Request Started - ID: {} ===", id);
        
        try {
            Policy policy = policyService.getById(id);
            
            if (policy == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "Policy not found");
                return ResponseEntity.notFound().build();
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", policy);
            
            log.info("Successfully retrieved policy - ID: {}, Name: {}", id, policy.getName());
            log.info("=== Get Policy Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("=== Get Policy Request Failed - ID: {} ===", id, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 根据名称获取策略内容
     */
    @GetMapping("/content/{name}")
    public ResponseEntity<Map<String, Object>> getPolicyContent(@PathVariable String name) {
        log.info("=== Get Policy Content Request Started - Name: {} ===", name);
        
        try {
            Policy policy = policyService.getByNameAndEnabled(name);
            
            if (policy == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "Policy not found or disabled");
                return ResponseEntity.notFound().build();
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("name", policy.getName());
            response.put("displayName", policy.getDisplayName());
            response.put("content", policy.getContent());
            response.put("description", policy.getDescription());
            response.put("type", policy.getType());
            
            log.info("Successfully retrieved policy content - Name: {}, ID: {}", name, policy.getId());
            log.info("=== Get Policy Content Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("=== Get Policy Content Request Failed - Name: {} ===", name, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 创建新策略
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createPolicy(@Valid @RequestBody Policy policy) {
        log.info("=== Create Policy Request Started - Name: {} ===", policy.getName());
        
        try {
            Policy created = policyService.create(policy);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", created);
            response.put("message", "Policy created successfully");
            
            log.info("Successfully created policy - ID: {}, Name: {}", created.getId(), created.getName());
            log.info("=== Create Policy Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            log.error("=== Create Policy Request Failed - Validation Error ===", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (Exception e) {
            log.error("=== Create Policy Request Failed ===", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 验证策略格式
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validatePolicy(@RequestBody Map<String, String> request) {
        log.info("=== Validate Policy Request Started ===");
        
        try {
            String policyInput = request.get("policy");
            if (policyInput == null || policyInput.trim().isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "Policy content is required");
                return ResponseEntity.badRequest().body(response);
            }

            String resolvedPolicy = policyService.resolvePolicy(policyInput);
            boolean isValid = policyService.isValidPolicyJson(resolvedPolicy);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("valid", isValid);
            response.put("resolvedContent", resolvedPolicy);
            
            if (isValid) {
                String formattedContent = policyService.formatPolicy(resolvedPolicy);
                response.put("formattedContent", formattedContent);
            }
            
            log.info("Policy validation completed - Valid: {}", isValid);
            log.info("=== Validate Policy Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("=== Validate Policy Request Failed ===", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 初始化默认策略
     */
    @PostMapping("/initialize")
    public ResponseEntity<Map<String, Object>> initializePolicies() {
        log.info("=== Initialize Policies Request Started ===");
        
        try {
            policyService.initializeDefaultPolicies();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Default policies initialized successfully");
            
            log.info("=== Initialize Policies Request Completed ===");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("=== Initialize Policies Request Failed ===", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
