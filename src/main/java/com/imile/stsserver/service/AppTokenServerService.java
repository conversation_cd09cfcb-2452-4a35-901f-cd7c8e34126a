package com.imile.stsserver.service;

import com.imile.stsserver.exception.StsException;
import com.imile.stsserver.model.sts.StsTokenResponse;
import com.imile.stsserver.model.sts.ProtocolType;

/**
 * App Token Server Service Interface
 */
public interface AppTokenServerService {
    
    /**
     * 获取STS Token
     *
     * @param accessKeyId Access Key ID
     * @param accessKeySecret Access Key Secret
     * @param roleArn Role ARN to assume
     * @param roleSessionName Session name for the role
     * @param policy Policy document (optional)
     * @param protocolType Protocol type (HTTP/HTTPS)
     * @param durationSeconds Duration in seconds
     * @return StsTokenResponse 阿里云官方格式的响应
     * @throws StsException if assume role fails
     */
    StsTokenResponse getStsToken(String accessKeyId, String accessKeySecret, String roleArn,
                                String roleSessionName, String policy, ProtocolType protocolType,
                                long durationSeconds) throws StsException;
}
