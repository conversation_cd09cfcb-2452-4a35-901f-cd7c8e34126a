package com.imile.stsserver.service.impl;

import com.aliyun.sts20150401.Client;
import com.aliyun.sts20150401.models.AssumeRoleRequest;
import com.aliyun.sts20150401.models.AssumeRoleResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.imile.stsserver.exception.StsException;
import com.imile.stsserver.model.sts.StsTokenResponse;
import com.imile.stsserver.model.sts.ProtocolType;
import com.imile.stsserver.service.AppTokenServerService;
import com.imile.stsserver.service.PolicyService;
import com.imile.stsserver.util.SessionNameGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * App Token Server Service Implementation
 */
@Slf4j
@Service
public class AppTokenServerServiceImpl implements AppTokenServerService {

    private final PolicyService policyService;

    public AppTokenServerServiceImpl(PolicyService policyService) {
        this.policyService = policyService;
    }

    @Override
    public StsTokenResponse getStsToken(String accessKeyId, String accessKeySecret, String roleArn,
                                       String roleSessionName, String policy, ProtocolType protocolType,
                                       long durationSeconds) throws StsException {

        // 自动生成 roleSessionName（如果为空）
        String actualSessionName = generateSessionName(roleSessionName);

        // 设置默认 durationSeconds（如果为 null 或无效值）
        long actualDurationSeconds = validateAndSetDefaultDuration(durationSeconds);

        log.info("Starting STS assume role operation - Role: {}, Session: {} (auto-generated: {}), Duration: {}s, Protocol: {}",
                roleArn, actualSessionName,
                roleSessionName == null || roleSessionName.trim().isEmpty(),
                actualDurationSeconds, protocolType);

        try {
            // Create STS client with provided credentials
            Client stsClient = createStsClient(accessKeyId, accessKeySecret, protocolType);

            // Create assume role request
            AssumeRoleRequest request = new AssumeRoleRequest()
                    .setRoleArn(roleArn)
                    .setRoleSessionName(actualSessionName)
                    .setDurationSeconds((long) actualDurationSeconds);

            // 解析并设置 policy
            if (policy != null && !policy.trim().isEmpty()) {
                String resolvedPolicy = policyService.resolvePolicy(policy);
                if (resolvedPolicy != null) {
                    // 验证 policy 格式
                    if (policyService.isValidPolicyJson(resolvedPolicy)) {
                        String formattedPolicy = policyService.formatPolicy(resolvedPolicy);
                        request.setPolicy(formattedPolicy);
                        log.info("Applied policy: {}", policy.startsWith("{") ? "inline JSON" : "policy name: " + policy);
                    } else {
                        throw new IllegalArgumentException("Invalid policy format: " + policy);
                    }
                }
            }
            
            // Execute assume role request
            RuntimeOptions runtime = new RuntimeOptions();
            com.aliyun.sts20150401.models.AssumeRoleResponse response = 
                    stsClient.assumeRoleWithOptions(request, runtime);
            
            // Convert response to STS token format
            StsTokenResponse tokenResponse = convertToStsTokenResponse(response, null);
            log.info("STS assume role operation completed successfully - Role: {}, Session: {}",
                    roleArn, actualSessionName);
            return tokenResponse;
            
        } catch (TeaException e) {
            log.error("STS assume role failed with TeaException: {}", e.getMessage(), e);
            String requestId = e.getData() != null ? String.valueOf(e.getData().get("RequestId")) : null;
            throw new StsException(e.getCode(), e.getMessage(), requestId);
        } catch (Exception e) {
            log.error("STS assume role failed with unexpected error: {}", e.getMessage(), e);
            throw new StsException("UNKNOWN_ERROR", "Failed to assume role: " + e.getMessage(), e);
        }
    }



    /**
     * Create STS client with specified credentials and protocol
     */
    private Client createStsClient(String accessKeyId, String accessKeySecret, ProtocolType protocolType) 
            throws Exception {
        
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret)
                .setEndpoint("sts.cn-hangzhou.aliyuncs.com")
                .setProtocol(protocolType == ProtocolType.HTTPS ? "HTTPS" : "HTTP");
        
        return new Client(config);
    }

    /**
     * Convert Aliyun STS response to STS token response
     */
    private StsTokenResponse convertToStsTokenResponse(
            com.aliyun.sts20150401.models.AssumeRoleResponse aliyunResponse,
            Instant requestStartTime) {

        StsTokenResponse tokenResponse = new StsTokenResponse();
        tokenResponse.setStatusCode("200");

        // 设置请求开始时间
        if (requestStartTime != null) {
            // 设置ISO 8601格式的请求开始时间
            LocalDateTime startDateTime = LocalDateTime.ofInstant(requestStartTime, ZoneId.systemDefault());
            String isoStartTime = startDateTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
            tokenResponse.setRequestStartTime(isoStartTime);

            // 设置请求开始时间戳（毫秒）
            tokenResponse.setRequestStartTimestamp(requestStartTime.toEpochMilli());

            log.debug("Request start time set - ISO: {}, Timestamp: {}", isoStartTime, requestStartTime.toEpochMilli());
        }

        // Convert credentials
        if (aliyunResponse.body.credentials != null) {
            AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials creds = aliyunResponse.body.credentials;
            tokenResponse.setAccessKeyId(creds.accessKeyId);
            tokenResponse.setAccessKeySecret(creds.accessKeySecret);
            tokenResponse.setSecurityToken(creds.securityToken);

            // Convert expiration time to ISO 8601 format and timestamp
            if (creds.expiration != null) {
                try {
                    Instant instant = Instant.parse(creds.expiration);
                    LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
                    String isoDateTime = localDateTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
                    tokenResponse.setExpiration(isoDateTime);

                    // 设置过期时间戳（Unix时间戳，秒）
                    tokenResponse.setExpirationTimestamp(instant.getEpochSecond());

                    log.debug("Token expiration set - ISO: {}, Timestamp: {}", isoDateTime, instant.getEpochSecond());
                } catch (Exception e) {
                    log.warn("Failed to parse expiration time: {}", creds.expiration, e);
                    tokenResponse.setExpiration(creds.expiration);
                    // 如果解析失败，尝试设置一个默认的过期时间戳（当前时间 + 1小时）
                    tokenResponse.setExpirationTimestamp(Instant.now().plusSeconds(3600).getEpochSecond());
                }
            }
        }

        return tokenResponse;
    }

    /**
     * 生成或验证 session name
     *
     * @param roleSessionName 输入的 session name
     * @return 有效的 session name
     */
    private String generateSessionName(String roleSessionName) {
        // 如果提供了 session name，验证并使用
        if (roleSessionName != null && !roleSessionName.trim().isEmpty()) {
            String trimmed = roleSessionName.trim();

            // 验证 session name 格式
            if (SessionNameGenerator.isValidSessionName(trimmed)) {
                log.debug("Using provided session name: {}", trimmed);
                return trimmed;
            } else {
                log.warn("Invalid session name format: {}, generating new one", trimmed);
            }
        }

        // 生成新的 session name
        String generated = SessionNameGenerator.generate();
        log.info("Generated new session name: {}", generated);
        return generated;
    }

    /**
     * 验证并设置默认的 duration seconds
     *
     * @param durationSeconds 输入的 duration
     * @return 有效的 duration seconds
     */
    private long validateAndSetDefaultDuration(Long durationSeconds) {
        // 默认值
        final long DEFAULT_DURATION = 3600L;
        final long MIN_DURATION = 900L;
        final long MAX_DURATION = 43200L;

        // 如果为 null，使用默认值
        if (durationSeconds == null) {
            log.info("Duration not specified, using default: {}s", DEFAULT_DURATION);
            return DEFAULT_DURATION;
        }

        // 验证范围
        if (durationSeconds < MIN_DURATION) {
            log.warn("Duration {}s is too short, using minimum: {}s", durationSeconds, MIN_DURATION);
            return MIN_DURATION;
        }

        if (durationSeconds > MAX_DURATION) {
            log.warn("Duration {}s is too long, using maximum: {}s", durationSeconds, MAX_DURATION);
            return MAX_DURATION;
        }

        log.debug("Using provided duration: {}s", durationSeconds);
        return durationSeconds;
    }

    @Override
    public StsTokenResponse getStsToken(String accessKeyId, String accessKeySecret, String roleArn,
                                       String roleSessionName, String policy, ProtocolType protocolType,
                                       long durationSeconds, Instant requestStartTime) throws StsException {

        // 自动生成 roleSessionName（如果为空）
        String actualSessionName = generateSessionName(roleSessionName);

        // 设置默认 durationSeconds（如果为 null 或无效值）
        long actualDurationSeconds = validateAndSetDefaultDuration(durationSeconds);

        log.info("Starting STS assume role operation with request start time - Role: {}, Session: {} (auto-generated: {}), Duration: {}s, Protocol: {}",
                roleArn, actualSessionName,
                roleSessionName == null || roleSessionName.trim().isEmpty(),
                actualDurationSeconds, protocolType);

        try {
            // Create STS client with provided credentials
            Client stsClient = createStsClient(accessKeyId, accessKeySecret, protocolType);

            // Create assume role request
            AssumeRoleRequest request = new AssumeRoleRequest()
                    .setRoleArn(roleArn)
                    .setRoleSessionName(actualSessionName)
                    .setDurationSeconds((long) actualDurationSeconds);

            // 解析并设置 policy
            if (policy != null && !policy.trim().isEmpty()) {
                String resolvedPolicy = policyService.resolvePolicy(policy);
                if (resolvedPolicy != null) {
                    // 验证 policy 格式
                    if (policyService.isValidPolicyJson(resolvedPolicy)) {
                        String formattedPolicy = policyService.formatPolicy(resolvedPolicy);
                        request.setPolicy(formattedPolicy);
                        log.info("Applied policy: {}", policy.startsWith("{") ? "inline JSON" : "policy name: " + policy);
                    } else {
                        throw new IllegalArgumentException("Invalid policy format: " + policy);
                    }
                }
            }

            // Execute assume role request
            RuntimeOptions runtime = new RuntimeOptions();
            com.aliyun.sts20150401.models.AssumeRoleResponse response =
                    stsClient.assumeRoleWithOptions(request, runtime);

            // Convert response to STS token format with request start time
            StsTokenResponse tokenResponse = convertToStsTokenResponse(response, requestStartTime);
            log.info("STS assume role operation completed successfully - Role: {}, Session: {}",
                    roleArn, actualSessionName);
            return tokenResponse;

        } catch (TeaException e) {
            log.error("STS assume role failed with TeaException: {}", e.getMessage(), e);
            String requestId = e.getData() != null ? String.valueOf(e.getData().get("RequestId")) : null;
            throw new StsException(e.getCode(), e.getMessage(), requestId);
        } catch (Exception e) {
            log.error("STS assume role failed with unexpected error: {}", e.getMessage(), e);
            throw new StsException("UNKNOWN_ERROR", "Failed to assume role: " + e.getMessage(), e);
        }
    }
}
