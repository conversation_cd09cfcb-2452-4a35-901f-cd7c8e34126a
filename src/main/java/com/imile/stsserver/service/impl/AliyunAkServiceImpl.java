package com.imile.stsserver.service.impl;

import com.imile.stsserver.entity.AliyunAk;
import com.imile.stsserver.mapper.AliyunAkMapper;
import com.imile.stsserver.service.AliyunAkService;
import com.imile.stsserver.util.EncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 阿里云AccessKey配置服务实现类
 */
@Slf4j
@Service
public class AliyunAkServiceImpl implements AliyunAkService {

    private final AliyunAkMapper aliyunAkMapper;

    public AliyunAkServiceImpl(AliyunAkMapper aliyunAkMapper) {
        this.aliyunAkMapper = aliyunAkMapper;
    }

    @Override
    @Cacheable(value = "aliyun-ak-cache", key = "'default'", unless = "#result == null")
    public AliyunAk getDefaultConfig() {
        log.debug("Getting default Aliyun AK configuration (from database)");

        AliyunAk config = aliyunAkMapper.selectFirstEnabled();
        if (config == null) {
            log.warn("No enabled Aliyun AK configuration found");
            return null;
        }

        // 解密敏感数据
        try {
            config.decryptToPlainData();
            log.info("Retrieved and decrypted default Aliyun AK configuration - ID: {}, AccessKeyID: {}",
                    config.getId(), config.getMaskedAccessKeyId());
            log.debug("Default Aliyun AK configuration cached");
        } catch (Exception e) {
            log.error("Failed to decrypt default Aliyun AK configuration - ID: {}", config.getId(), e);
            return null;
        }

        return config;
    }

    @Override
    @Cacheable(value = "aliyun-ak-cache", key = "'accessKeyId:' + T(com.imile.stsserver.util.EncryptionUtil).generateMD5Hash(#accessKeyId)", unless = "#result == null")
    public AliyunAk getByAccessKeyId(String accessKeyId) {
        log.debug("Getting Aliyun AK configuration by AccessKeyID: {} (from database)",
                EncryptionUtil.maskSensitiveData(accessKeyId, 8));

        // 生成AccessKeyID的哈希值用于查询
        String accessKeyIdHash = EncryptionUtil.generateMD5Hash(accessKeyId);
        AliyunAk config = aliyunAkMapper.selectByAccessKeyIdHash(accessKeyIdHash);

        if (config == null) {
            log.warn("Aliyun AK configuration not found for AccessKeyID: {}",
                    EncryptionUtil.maskSensitiveData(accessKeyId, 8));
            return null;
        }

        // 解密并验证AccessKeyID是否匹配
        try {
            config.decryptToPlainData();
            if (!accessKeyId.equals(config.getAccessKeyId())) {
                log.warn("AccessKeyID verification failed for configuration ID: {} - Expected: {}, Actual: {}",
                        config.getId(), EncryptionUtil.maskSensitiveData(accessKeyId, 8),
                        config.getMaskedAccessKeyId());
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to decrypt Aliyun AK configuration for verification - ID: {}", config.getId(), e);
            return null;
        }

        log.info("Retrieved and decrypted Aliyun AK configuration by AccessKeyID - ID: {}, Status: {}",
                config.getId(), config.getStatus());
        log.debug("Aliyun AK configuration cached for AccessKeyID");
        return config;
    }

    @Override
    @Cacheable(value = "aliyun-ak-cache", key = "'token:' + #token", unless = "#result == null")
    public AliyunAk getByTokenAndEnabled(String token) {
        log.debug("Getting enabled Aliyun AK configuration by token: {} (from database)",
                EncryptionUtil.maskSensitiveData(token, 8));

        AliyunAk config = aliyunAkMapper.selectByTokenAndEnabled(token);
        if (config == null) {
            log.warn("No enabled Aliyun AK configuration found for token: {}",
                    EncryptionUtil.maskSensitiveData(token, 8));
            return null;
        }

        // 解密敏感数据
        try {
            config.decryptToPlainData();
            log.info("Retrieved and decrypted enabled Aliyun AK configuration by token - ID: {}, AccessKeyID: {}, Status: {}",
                    config.getId(), config.getMaskedAccessKeyId(), config.getStatus());
            log.debug("Aliyun AK configuration cached for token");
        } catch (Exception e) {
            log.error("Failed to decrypt Aliyun AK configuration for token - ID: {}", config.getId(), e);
            return null;
        }

        return config;
    }

    @Override
    @Cacheable(value = "aliyun-ak-cache", key = "'id:' + #id", unless = "#result == null")
    public AliyunAk getById(Long id) {
        log.debug("Getting Aliyun AK configuration by ID: {} (from database)", id);

        AliyunAk config = aliyunAkMapper.selectById(id);
        if (config == null) {
            log.warn("Aliyun AK configuration not found for ID: {}", id);
            return null;
        }

        // 解密敏感数据
        try {
            config.decryptToPlainData();
            log.info("Retrieved and decrypted Aliyun AK configuration by ID - AccessKeyID: {}, Status: {}",
                    config.getMaskedAccessKeyId(), config.getStatus());
            log.debug("Aliyun AK configuration cached for ID: {}", id);
        } catch (Exception e) {
            log.error("Failed to decrypt Aliyun AK configuration - ID: {}", id, e);
            // 返回未解密的配置，让调用者决定如何处理
        }

        return config;
    }

    @Override
    @Cacheable(value = "aliyun-ak-cache", key = "'all-enabled'")
    public List<AliyunAk> getAllEnabled() {
        log.debug("Getting all enabled Aliyun AK configurations (from database)");

        List<AliyunAk> configs = aliyunAkMapper.selectAllEnabled();
        log.info("Retrieved {} enabled Aliyun AK configurations", configs.size());
        log.debug("All enabled Aliyun AK configurations cached");

        return configs;
    }

    @Override
    @Cacheable(value = "aliyun-ak-cache", key = "'all'")
    public List<AliyunAk> getAll() {
        log.debug("Getting all Aliyun AK configurations (from database)");

        List<AliyunAk> configs = aliyunAkMapper.selectAll();
        log.info("Retrieved {} Aliyun AK configurations", configs.size());
        log.debug("All Aliyun AK configurations cached");

        return configs;
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = "aliyun-ak-cache", key = "'all'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'all-enabled'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'default'")
    })
    public AliyunAk create(AliyunAk aliyunAk) {
        log.info("Creating new Aliyun AK configuration - AccessKeyID: {}",
                aliyunAk.getMaskedAccessKeyId());

        // 验证必要的明文字段
        if (aliyunAk.getAccessKeyId() == null || aliyunAk.getAccessKeySecret() == null || aliyunAk.getRoleArn() == null) {
            log.error("Missing required plain text fields for encryption");
            throw new IllegalArgumentException("AccessKeyID, AccessKeySecret, and RoleArn are required");
        }

        // 检查AccessKeyID是否已存在
        if (existsByAccessKeyId(aliyunAk.getAccessKeyId())) {
            log.error("AccessKeyID already exists: {}", aliyunAk.getMaskedAccessKeyId());
            throw new IllegalArgumentException("AccessKeyID already exists");
        }

        // 加密敏感数据
        aliyunAk.setPlainData(aliyunAk.getAccessKeyId(), aliyunAk.getAccessKeySecret(), aliyunAk.getRoleArn());

        // 验证加密数据
        if (!aliyunAk.isEncryptedDataValid()) {
            log.error("Failed to encrypt sensitive data");
            throw new RuntimeException("Failed to encrypt sensitive data");
        }

        // 设置默认状态为启用
        if (aliyunAk.getStatus() == null) {
            aliyunAk.setStatus(1);
        }

        int result = aliyunAkMapper.insert(aliyunAk);
        if (result > 0) {
            log.info("Successfully created Aliyun AK configuration - ID: {}, AccessKeyID: {}",
                    aliyunAk.getId(), aliyunAk.getMaskedAccessKeyId());

            // 清除明文数据（安全考虑）
            aliyunAk.clearPlainData();
            return aliyunAk;
        } else {
            log.error("Failed to create Aliyun AK configuration");
            throw new RuntimeException("Failed to create Aliyun AK configuration");
        }
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = "aliyun-ak-cache", key = "'id:' + #aliyunAk.id"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'all'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'all-enabled'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'default'")
    })
    public AliyunAk update(AliyunAk aliyunAk) {
        log.info("Updating Aliyun AK configuration - ID: {}, AccessKeyID: {}", 
                aliyunAk.getId(), aliyunAk.getMaskedAccessKeyId());
        
        // 检查配置是否存在
        AliyunAk existing = getById(aliyunAk.getId());
        if (existing == null) {
            log.error("Aliyun AK configuration not found for ID: {}", aliyunAk.getId());
            throw new IllegalArgumentException("Configuration not found");
        }
        
        int result = aliyunAkMapper.updateById(aliyunAk);
        if (result > 0) {
            log.info("Successfully updated Aliyun AK configuration - ID: {}", aliyunAk.getId());
            return getById(aliyunAk.getId());
        } else {
            log.error("Failed to update Aliyun AK configuration - ID: {}", aliyunAk.getId());
            throw new RuntimeException("Failed to update Aliyun AK configuration");
        }
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = "aliyun-ak-cache", key = "'id:' + #id"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'all'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'all-enabled'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'default'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'enabled-count'")
    })
    public boolean enable(Long id) {
        log.info("Enabling Aliyun AK configuration - ID: {}", id);
        
        int result = aliyunAkMapper.updateStatus(id, 1);
        boolean success = result > 0;
        
        if (success) {
            log.info("Successfully enabled Aliyun AK configuration - ID: {}", id);
        } else {
            log.error("Failed to enable Aliyun AK configuration - ID: {}", id);
        }
        
        return success;
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = "aliyun-ak-cache", key = "'id:' + #id"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'all'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'all-enabled'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'default'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'enabled-count'")
    })
    public boolean disable(Long id) {
        log.info("Disabling Aliyun AK configuration - ID: {}", id);
        
        int result = aliyunAkMapper.updateStatus(id, 0);
        boolean success = result > 0;
        
        if (success) {
            log.info("Successfully disabled Aliyun AK configuration - ID: {}", id);
        } else {
            log.error("Failed to disable Aliyun AK configuration - ID: {}", id);
        }
        
        return success;
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = "aliyun-ak-cache", key = "'id:' + #id"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'all'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'all-enabled'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'default'"),
        @CacheEvict(value = "aliyun-ak-cache", key = "'enabled-count'")
    })
    public boolean delete(Long id) {
        log.info("Deleting Aliyun AK configuration - ID: {}", id);
        
        // 检查配置是否存在
        AliyunAk existing = getById(id);
        if (existing == null) {
            log.warn("Aliyun AK configuration not found for deletion - ID: {}", id);
            return false;
        }
        
        int result = aliyunAkMapper.deleteById(id);
        boolean success = result > 0;
        
        if (success) {
            log.info("Successfully deleted Aliyun AK configuration - ID: {}, AccessKeyID: {}", 
                    id, existing.getMaskedAccessKeyId());
        } else {
            log.error("Failed to delete Aliyun AK configuration - ID: {}", id);
        }
        
        return success;
    }

    @Override
    @Cacheable(value = "aliyun-ak-cache", key = "'exists:' + T(com.imile.stsserver.util.EncryptionUtil).generateMD5Hash(#accessKeyId)")
    public boolean existsByAccessKeyId(String accessKeyId) {
        log.debug("Checking if AccessKeyID exists: {} (from database)",
                EncryptionUtil.maskSensitiveData(accessKeyId, 8));

        // 生成AccessKeyID的哈希值用于查询
        String accessKeyIdHash = EncryptionUtil.generateMD5Hash(accessKeyId);
        int count = aliyunAkMapper.existsByAccessKeyIdHash(accessKeyIdHash);
        boolean exists = count > 0;

        log.debug("AccessKeyID exists: {} (cached)", exists);
        return exists;
    }

    @Override
    @Cacheable(value = "aliyun-ak-cache", key = "'enabled-count'")
    public int getEnabledCount() {
        log.debug("Getting enabled Aliyun AK configuration count (from database)");

        int count = aliyunAkMapper.countEnabled();
        log.debug("Enabled Aliyun AK configuration count: {} (cached)", count);

        return count;
    }
}
