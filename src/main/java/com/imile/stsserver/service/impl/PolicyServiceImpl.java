package com.imile.stsserver.service.impl;

import com.imile.stsserver.entity.Policy;
import com.imile.stsserver.mapper.PolicyMapper;
import com.imile.stsserver.service.PolicyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 策略服务实现类
 */
@Slf4j
@Service
public class PolicyServiceImpl implements PolicyService {

    private final PolicyMapper policyMapper;
    private final CacheManager cacheManager;

    public PolicyServiceImpl(PolicyMapper policyMapper, CacheManager cacheManager) {
        this.policyMapper = policyMapper;
        this.cacheManager = cacheManager;
    }

    @Override
    @Cacheable(value = "policy-cache", key = "'id:' + #id", unless = "#result == null")
    public Policy getById(Long id) {
        log.debug("Getting policy by ID: {} (from database)", id);
        Policy policy = policyMapper.selectById(id);
        if (policy == null) {
            log.warn("Policy not found for ID: {}", id);
        } else {
            log.debug("Policy found and cached - ID: {}, Name: {}", id, policy.getName());
        }
        return policy;
    }

    @Override
    @Cacheable(value = "policy-cache", key = "'name:' + #name", unless = "#result == null")
    public Policy getByName(String name) {
        log.debug("Getting policy by name: {} (from database)", name);
        Policy policy = policyMapper.selectByName(name);
        if (policy == null) {
            log.warn("Policy not found for name: {}", name);
        } else {
            log.debug("Policy found and cached - Name: {}, ID: {}", name, policy.getId());
        }
        return policy;
    }

    @Override
    @Cacheable(value = "policy-cache", key = "'enabled:' + #name", unless = "#result == null")
    public Policy getByNameAndEnabled(String name) {
        log.debug("Getting enabled policy by name: {} (from database)", name);
        Policy policy = policyMapper.selectByNameAndEnabled(name);
        if (policy == null) {
            log.warn("Enabled policy not found for name: {}", name);
        } else {
            log.debug("Enabled policy found and cached - Name: {}, ID: {}", name, policy.getId());
        }
        return policy;
    }

    @Override
    @Cacheable(value = "policy-cache", key = "'all'")
    public List<Policy> getAll() {
        log.debug("Getting all policies (from database)");
        List<Policy> policies = policyMapper.selectAll();
        log.debug("Found {} policies and cached", policies.size());
        return policies;
    }

    @Override
    @Cacheable(value = "policy-cache", key = "'all-enabled'")
    public List<Policy> getAllEnabled() {
        log.debug("Getting all enabled policies (from database)");
        List<Policy> policies = policyMapper.selectAllEnabled();
        log.debug("Found {} enabled policies and cached", policies.size());
        return policies;
    }

    @Override
    @Cacheable(value = "policy-cache", key = "'type:' + #type")
    public List<Policy> getByType(String type) {
        log.debug("Getting policies by type: {} (from database)", type);
        List<Policy> policies = policyMapper.selectByType(type);
        log.debug("Found {} policies for type {} and cached", policies.size(), type);
        return policies;
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = "policy-cache", key = "'all'"),
        @CacheEvict(value = "policy-cache", key = "'all-enabled'"),
        @CacheEvict(value = "policy-cache", key = "'type:' + #policy.type", condition = "#policy.type != null")
    })
    public Policy create(Policy policy) {
        log.info("Creating new policy: {}", policy.getName());

        // 验证策略名称是否已存在
        if (existsByName(policy.getName())) {
            throw new IllegalArgumentException("Policy name already exists: " + policy.getName());
        }

        // 验证策略内容
        if (!isValidPolicyJson(policy.getContent())) {
            throw new IllegalArgumentException("Invalid policy JSON format");
        }

        // 设置默认状态
        if (policy.getStatus() == null) {
            policy.setStatus(1); // 默认启用
        }

        int result = policyMapper.insert(policy);
        if (result > 0) {
            log.info("Successfully created policy - ID: {}, Name: {}", policy.getId(), policy.getName());
            Policy created = getById(policy.getId());
            log.info("Policy cache cleared after creation");
            return created;
        } else {
            log.error("Failed to create policy: {}", policy.getName());
            throw new RuntimeException("Failed to create policy");
        }
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = "policy-cache", key = "'id:' + #policy.id"),
        @CacheEvict(value = "policy-cache", key = "'name:' + #policy.name"),
        @CacheEvict(value = "policy-cache", key = "'enabled:' + #policy.name"),
        @CacheEvict(value = "policy-cache", key = "'all'"),
        @CacheEvict(value = "policy-cache", key = "'all-enabled'"),
        @CacheEvict(value = "policy-cache", key = "'type:' + #policy.type", condition = "#policy.type != null")
    })
    public Policy update(Policy policy) {
        log.info("Updating policy - ID: {}, Name: {}", policy.getId(), policy.getName());

        // 验证策略是否存在
        Policy existing = getById(policy.getId());
        if (existing == null) {
            throw new IllegalArgumentException("Policy not found for ID: " + policy.getId());
        }

        // 验证策略内容
        if (!isValidPolicyJson(policy.getContent())) {
            throw new IllegalArgumentException("Invalid policy JSON format");
        }

        int result = policyMapper.updateById(policy);
        if (result > 0) {
            log.info("Successfully updated policy - ID: {}", policy.getId());
            log.info("Policy cache cleared after update");
            return getById(policy.getId());
        } else {
            log.error("Failed to update policy - ID: {}", policy.getId());
            throw new RuntimeException("Failed to update policy");
        }
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = "policy-cache", key = "'id:' + #id"),
        @CacheEvict(value = "policy-cache", key = "'all'"),
        @CacheEvict(value = "policy-cache", key = "'all-enabled'")
    })
    public boolean enable(Long id) {
        log.info("Enabling policy - ID: {}", id);

        // 获取策略信息用于清除相关缓存
        Policy policy = policyMapper.selectById(id);

        int result = policyMapper.updateStatus(id, 1);
        boolean success = result > 0;

        if (success) {
            log.info("Successfully enabled policy - ID: {}", id);
            // 清除名称相关的缓存
            if (policy != null) {
                clearPolicyNameCache(policy.getName());
                log.info("Policy cache cleared after enable - Name: {}", policy.getName());
            }
        } else {
            log.error("Failed to enable policy - ID: {}", id);
        }

        return success;
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = "policy-cache", key = "'id:' + #id"),
        @CacheEvict(value = "policy-cache", key = "'all'"),
        @CacheEvict(value = "policy-cache", key = "'all-enabled'")
    })
    public boolean disable(Long id) {
        log.info("Disabling policy - ID: {}", id);

        // 获取策略信息用于清除相关缓存
        Policy policy = policyMapper.selectById(id);

        int result = policyMapper.updateStatus(id, 0);
        boolean success = result > 0;

        if (success) {
            log.info("Successfully disabled policy - ID: {}", id);
            // 清除名称相关的缓存
            if (policy != null) {
                clearPolicyNameCache(policy.getName());
                log.info("Policy cache cleared after disable - Name: {}", policy.getName());
            }
        } else {
            log.error("Failed to disable policy - ID: {}", id);
        }

        return success;
    }

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = "policy-cache", key = "'id:' + #id"),
        @CacheEvict(value = "policy-cache", key = "'all'"),
        @CacheEvict(value = "policy-cache", key = "'all-enabled'")
    })
    public boolean delete(Long id) {
        log.info("Deleting policy - ID: {}", id);

        // 获取策略信息用于清除相关缓存
        Policy policy = policyMapper.selectById(id);

        int result = policyMapper.deleteById(id);
        boolean success = result > 0;

        if (success) {
            log.info("Successfully deleted policy - ID: {}", id);
            // 清除名称相关的缓存
            if (policy != null) {
                clearPolicyNameCache(policy.getName());
                log.info("Policy cache cleared after delete - Name: {}", policy.getName());
            }
        } else {
            log.error("Failed to delete policy - ID: {}", id);
        }

        return success;
    }

    @Override
    @Cacheable(value = "policy-cache", key = "'exists:' + #name")
    public boolean existsByName(String name) {
        log.debug("Checking if policy name exists: {} (from database)", name);
        int count = policyMapper.existsByName(name);
        boolean exists = count > 0;
        log.debug("Policy name exists check cached - Name: {}, Exists: {}", name, exists);
        return exists;
    }

    @Override
    @Cacheable(value = "policy-cache", key = "'enabled-count'")
    public int getEnabledCount() {
        log.debug("Getting enabled policy count (from database)");
        int count = policyMapper.countEnabled();
        log.debug("Enabled policy count cached: {}", count);
        return count;
    }

    @Override
    public String resolvePolicy(String policyInput) {
        if (!StringUtils.hasText(policyInput)) {
            return null;
        }

        // 检查是否是JSON字符串（以{开头）
        String trimmed = policyInput.trim();
        if (trimmed.startsWith("{")) {
            log.debug("Policy input appears to be JSON, returning directly");
            return trimmed;
        }

        // 否则认为是策略名称，从数据库获取
        log.debug("Resolving policy by name: {}", policyInput);
        Policy policy = getByNameAndEnabled(policyInput);
        if (policy != null) {
            log.info("Found policy by name: {} -> ID: {}", policyInput, policy.getId());
            return policy.getContent();
        }

        log.warn("Policy not found by name: {}", policyInput);
        throw new IllegalArgumentException("Policy not found: " + policyInput);
    }

    @Override
    public boolean isValidPolicyJson(String policyJson) {
        if (!StringUtils.hasText(policyJson)) {
            return false;
        }

        try {
            // 简单验证：检查是否包含必要的字段
            String trimmed = policyJson.trim();
            return trimmed.startsWith("{") && 
                   trimmed.endsWith("}") && 
                   trimmed.contains("Statement") && 
                   trimmed.contains("Version");
        } catch (Exception e) {
            log.warn("Invalid policy JSON format: {}", policyJson, e);
            return false;
        }
    }

    @Override
    public String formatPolicy(String policyJson) {
        if (!StringUtils.hasText(policyJson)) {
            return null;
        }

        // 移除多余的空格和换行，但保持JSON结构
        return policyJson.replaceAll("\\s+", " ").trim();
    }

    @Override
    @Transactional
    public void initializeDefaultPolicies() {
        log.info("=== Initializing Default Policies ===");
        
        try {
            // 检查是否已有策略数据
            int existingCount = getEnabledCount();
            if (existingCount > 0) {
                log.info("Found {} existing enabled policies, skipping initialization", existingCount);
                return;
            }

            // 初始化默认策略
            initializePolicyFromResource("oss_read_policy", "OSS Read Policy", 
                "policies/oss_read_policy.json", "OSS", "OSS读取权限策略");
            
            initializePolicyFromResource("oss_write_policy", "OSS Write Policy", 
                "policies/oss_write_policy.json", "OSS", "OSS写入权限策略");
            
            initializePolicyFromResource("read_policy", "Log Read Policy", 
                "policies/read_policy.txt", "LOG", "日志读取权限策略");
            
            initializePolicyFromResource("write_policy", "Log Write Policy", 
                "policies/write_policy.txt", "LOG", "日志写入权限策略");

            log.info("=== Default Policies Initialization Completed ===");
            
        } catch (Exception e) {
            log.error("=== Default Policies Initialization Failed ===", e);
            throw new RuntimeException("Failed to initialize default policies", e);
        }
    }

    /**
     * 从资源文件初始化策略
     */
    private void initializePolicyFromResource(String name, String displayName, 
                                            String resourcePath, String type, String description) {
        try {
            log.info("Initializing policy: {} from {}", name, resourcePath);
            
            ClassPathResource resource = new ClassPathResource(resourcePath);
            if (!resource.exists()) {
                log.warn("Policy resource not found: {}", resourcePath);
                return;
            }

            String content = new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            
            Policy policy = new Policy();
            policy.setName(name);
            policy.setDisplayName(displayName);
            policy.setContent(content.trim());
            policy.setDescription(description);
            policy.setType(type);
            policy.setStatus(1); // 启用

            create(policy);
            log.info("Successfully initialized policy: {}", name);
            
        } catch (IOException e) {
            log.error("Failed to read policy resource: {}", resourcePath, e);
        } catch (Exception e) {
            log.error("Failed to initialize policy: {}", name, e);
        }
    }

    /**
     * 手动清除策略名称相关的缓存
     */
    private void clearPolicyNameCache(String policyName) {
        try {
            Cache cache = cacheManager.getCache("policy-cache");
            if (cache != null) {
                cache.evict("name:" + policyName);
                cache.evict("enabled:" + policyName);
                cache.evict("exists:" + policyName);
                log.debug("Manually cleared cache for policy name: {}", policyName);
            }
        } catch (Exception e) {
            log.warn("Failed to manually clear cache for policy name: {}", policyName, e);
        }
    }
}
