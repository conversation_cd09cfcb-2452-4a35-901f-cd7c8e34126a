package com.imile.stsserver.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 缓存管理服务
 * 提供缓存统计、清理等管理功能
 */
@Slf4j
@Service
public class CacheManagementService {

    private final CacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;

    public CacheManagementService(CacheManager cacheManager, RedisTemplate<String, Object> redisTemplate) {
        this.cacheManager = cacheManager;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        log.debug("Getting cache statistics");
        
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 获取所有缓存名称
            for (String cacheName : cacheManager.getCacheNames()) {
                Cache cache = cacheManager.getCache(cacheName);
                if (cache != null) {
                    Map<String, Object> cacheStats = new HashMap<>();
                    
                    // 获取Redis中该缓存的键数量
                    String pattern = "sts-cache::" + cacheName + ":*";
                    Set<String> keys = redisTemplate.keys(pattern);
                    int keyCount = keys != null ? keys.size() : 0;
                    
                    cacheStats.put("keyCount", keyCount);
                    cacheStats.put("cacheName", cacheName);
                    
                    stats.put(cacheName, cacheStats);
                }
            }
            
            // 总体统计
            Set<String> allKeys = redisTemplate.keys("sts-cache::*");
            stats.put("totalKeys", allKeys != null ? allKeys.size() : 0);
            stats.put("timestamp", System.currentTimeMillis());
            
            log.debug("Cache statistics generated successfully");
            
        } catch (Exception e) {
            log.error("Failed to get cache statistics", e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    /**
     * 清除指定缓存的所有数据
     */
    public boolean clearCache(String cacheName) {
        log.info("Clearing cache: {}", cacheName);
        
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                log.info("Successfully cleared cache: {}", cacheName);
                return true;
            } else {
                log.warn("Cache not found: {}", cacheName);
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to clear cache: {}", cacheName, e);
            return false;
        }
    }

    /**
     * 清除所有缓存
     */
    public boolean clearAllCaches() {
        log.info("Clearing all caches");
        
        try {
            int clearedCount = 0;
            for (String cacheName : cacheManager.getCacheNames()) {
                if (clearCache(cacheName)) {
                    clearedCount++;
                }
            }
            
            log.info("Successfully cleared {} caches", clearedCount);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to clear all caches", e);
            return false;
        }
    }

    /**
     * 清除策略相关的所有缓存
     */
    public boolean clearPolicyCache() {
        log.info("Clearing policy cache");
        return clearCache("policy-cache");
    }

    /**
     * 清除指定策略的缓存
     */
    public boolean clearPolicyCacheByName(String policyName) {
        log.info("Clearing policy cache for name: {}", policyName);
        
        try {
            Cache cache = cacheManager.getCache("policy-cache");
            if (cache != null) {
                // 清除与该策略名称相关的所有缓存键
                cache.evict("name:" + policyName);
                cache.evict("enabled:" + policyName);
                
                log.info("Successfully cleared policy cache for name: {}", policyName);
                return true;
            } else {
                log.warn("Policy cache not found");
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to clear policy cache for name: {}", policyName, e);
            return false;
        }
    }

    /**
     * 清除指定策略ID的缓存
     */
    public boolean clearPolicyCacheById(Long policyId) {
        log.info("Clearing policy cache for ID: {}", policyId);
        
        try {
            Cache cache = cacheManager.getCache("policy-cache");
            if (cache != null) {
                cache.evict("id:" + policyId);
                log.info("Successfully cleared policy cache for ID: {}", policyId);
                return true;
            } else {
                log.warn("Policy cache not found");
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to clear policy cache for ID: {}", policyId, e);
            return false;
        }
    }

    /**
     * 预热策略缓存
     */
    public boolean warmupPolicyCache() {
        log.info("Warming up policy cache");
        
        try {
            // 这里可以预加载一些常用的策略到缓存中
            // 例如：加载所有启用的策略
            log.info("Policy cache warmup completed");
            return true;
            
        } catch (Exception e) {
            log.error("Failed to warmup policy cache", e);
            return false;
        }
    }

    /**
     * 获取缓存健康状态
     */
    public Map<String, Object> getCacheHealth() {
        log.debug("Checking cache health");
        
        Map<String, Object> health = new HashMap<>();
        boolean allHealthy = true;
        
        try {
            // 检查Redis连接
            String ping = redisTemplate.getConnectionFactory().getConnection().ping();
            boolean redisHealthy = "PONG".equals(ping);
            health.put("redis", Map.of(
                "status", redisHealthy ? "UP" : "DOWN",
                "ping", ping
            ));
            allHealthy &= redisHealthy;
            
            // 检查缓存管理器
            boolean cacheManagerHealthy = cacheManager != null;
            health.put("cacheManager", Map.of(
                "status", cacheManagerHealthy ? "UP" : "DOWN",
                "cacheNames", cacheManagerHealthy ? cacheManager.getCacheNames() : "N/A"
            ));
            allHealthy &= cacheManagerHealthy;
            
            health.put("overall", allHealthy ? "UP" : "DOWN");
            health.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("Failed to check cache health", e);
            health.put("overall", "DOWN");
            health.put("error", e.getMessage());
        }
        
        return health;
    }
}
