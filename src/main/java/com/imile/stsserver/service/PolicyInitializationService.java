package com.imile.stsserver.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 策略初始化服务
 * 在应用启动时初始化默认的策略数据
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "app.policy-initialization.enabled", havingValue = "true", matchIfMissing = false)
public class PolicyInitializationService implements CommandLineRunner {

    private final PolicyService policyService;

    public PolicyInitializationService(PolicyService policyService) {
        this.policyService = policyService;
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("=== Policy Initialization Started ===");
        
        try {
            // 检查是否已有策略数据
            int existingCount = policyService.getEnabledCount();
            if (existingCount > 0) {
                log.info("Found {} existing enabled policies, skipping initialization", existingCount);
                log.info("=== Policy Initialization Skipped ===");
                return;
            }
            
            log.info("No enabled policies found, initializing default policies...");
            
            // 初始化默认策略
            policyService.initializeDefaultPolicies();
            
            int newCount = policyService.getEnabledCount();
            log.info("Successfully initialized {} default policies", newCount);
            log.info("=== Policy Initialization Completed Successfully ===");
            
        } catch (Exception e) {
            log.error("=== Policy Initialization Failed ===");
            log.error("Failed to initialize default policies", e);
            
            // 不抛出异常，避免影响应用启动
            log.warn("Application will continue without default policies. " +
                    "Please manually initialize policies via API: POST /api/policy/initialize");
        }
    }

    /**
     * 手动初始化策略数据（用于测试或重新初始化）
     */
    public void initializePolicies() {
        log.info("=== Manual Policy Initialization Started ===");
        
        try {
            policyService.initializeDefaultPolicies();
            
            int count = policyService.getEnabledCount();
            log.info("Manual policy initialization completed - {} policies enabled", count);
            
        } catch (Exception e) {
            log.error("Manual policy initialization failed", e);
            throw new RuntimeException("Failed to initialize policies manually", e);
        }
    }
}
