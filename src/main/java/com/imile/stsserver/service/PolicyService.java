package com.imile.stsserver.service;

import com.imile.stsserver.entity.Policy;

import java.util.List;

/**
 * 策略服务接口
 */
public interface PolicyService {

    /**
     * 根据ID获取策略
     *
     * @param id 策略ID
     * @return 策略信息
     */
    Policy getById(Long id);

    /**
     * 根据名称获取策略
     *
     * @param name 策略名称
     * @return 策略信息
     */
    Policy getByName(String name);

    /**
     * 根据名称获取启用的策略
     *
     * @param name 策略名称
     * @return 策略信息
     */
    Policy getByNameAndEnabled(String name);

    /**
     * 获取所有策略
     *
     * @return 策略列表
     */
    List<Policy> getAll();

    /**
     * 获取所有启用的策略
     *
     * @return 策略列表
     */
    List<Policy> getAllEnabled();

    /**
     * 根据类型获取策略
     *
     * @param type 策略类型
     * @return 策略列表
     */
    List<Policy> getByType(String type);

    /**
     * 创建新策略
     *
     * @param policy 策略信息
     * @return 创建的策略
     */
    Policy create(Policy policy);

    /**
     * 更新策略
     *
     * @param policy 策略信息
     * @return 更新的策略
     */
    Policy update(Policy policy);

    /**
     * 启用策略
     *
     * @param id 策略ID
     * @return 是否成功
     */
    boolean enable(Long id);

    /**
     * 禁用策略
     *
     * @param id 策略ID
     * @return 是否成功
     */
    boolean disable(Long id);

    /**
     * 删除策略
     *
     * @param id 策略ID
     * @return 是否成功
     */
    boolean delete(Long id);

    /**
     * 检查策略名称是否存在
     *
     * @param name 策略名称
     * @return 是否存在
     */
    boolean existsByName(String name);

    /**
     * 获取启用策略数量
     *
     * @return 数量
     */
    int getEnabledCount();

    /**
     * 解析策略参数
     * 如果是策略名称，则从数据库获取策略内容
     * 如果是JSON字符串，则直接返回
     *
     * @param policyInput 策略输入（策略名称或JSON字符串）
     * @return 策略JSON内容
     */
    String resolvePolicy(String policyInput);

    /**
     * 验证策略JSON格式是否正确
     *
     * @param policyJson 策略JSON字符串
     * @return 是否有效
     */
    boolean isValidPolicyJson(String policyJson);

    /**
     * 格式化策略JSON
     *
     * @param policyJson 策略JSON字符串
     * @return 格式化后的JSON字符串
     */
    String formatPolicy(String policyJson);

    /**
     * 初始化默认策略数据
     * 从策略文件导入到数据库
     */
    void initializeDefaultPolicies();
}
