package com.imile.stsserver.service;

import com.imile.stsserver.entity.AliyunAk;

import java.util.List;

/**
 * 阿里云AccessKey配置服务接口
 */
public interface AliyunAkService {

    /**
     * 获取默认的阿里云配置（第一个启用的配置）
     * 
     * @return 阿里云配置
     */
    AliyunAk getDefaultConfig();

    /**
     * 根据AccessKeyID获取配置
     *
     * @param accessKeyId AccessKeyID
     * @return 阿里云配置
     */
    AliyunAk getByAccessKeyId(String accessKeyId);

    /**
     * 根据认证token获取启用的配置
     *
     * @param token 认证token（对应AccessKeyID_hash字段）
     * @return 阿里云配置
     */
    AliyunAk getByTokenAndEnabled(String token);

    /**
     * 根据ID获取配置
     * 
     * @param id 配置ID
     * @return 阿里云配置
     */
    AliyunAk getById(Long id);

    /**
     * 获取所有启用的配置
     * 
     * @return 配置列表
     */
    List<AliyunAk> getAllEnabled();

    /**
     * 获取所有配置
     * 
     * @return 配置列表
     */
    List<AliyunAk> getAll();

    /**
     * 创建新配置
     * 
     * @param aliyunAk 配置信息
     * @return 创建的配置
     */
    AliyunAk create(AliyunAk aliyunAk);

    /**
     * 更新配置
     * 
     * @param aliyunAk 配置信息
     * @return 更新的配置
     */
    AliyunAk update(AliyunAk aliyunAk);

    /**
     * 启用配置
     * 
     * @param id 配置ID
     * @return 是否成功
     */
    boolean enable(Long id);

    /**
     * 禁用配置
     * 
     * @param id 配置ID
     * @return 是否成功
     */
    boolean disable(Long id);

    /**
     * 删除配置
     * 
     * @param id 配置ID
     * @return 是否成功
     */
    boolean delete(Long id);

    /**
     * 检查AccessKeyID是否存在
     * 
     * @param accessKeyId AccessKeyID
     * @return 是否存在
     */
    boolean existsByAccessKeyId(String accessKeyId);

    /**
     * 获取启用配置数量
     * 
     * @return 数量
     */
    int getEnabledCount();
}
