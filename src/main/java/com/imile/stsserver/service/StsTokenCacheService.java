package com.imile.stsserver.service;

import com.imile.stsserver.model.sts.StsTokenResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.concurrent.TimeUnit;

/**
 * STS Token缓存服务
 * 基于Redis实现高性能缓存，支持高并发访问
 */
@Slf4j
@Service
public class StsTokenCacheService {

    private final RedisTemplate<String, String> redisTemplate;
    private final ObjectMapper objectMapper;
    
    // 缓存配置
    private static final String CACHE_PREFIX = "sts:token:";
    private static final Duration DEFAULT_CACHE_DURATION = Duration.ofMinutes(10);
    private static final String CACHE_VERSION = "v1";

    public StsTokenCacheService(@Qualifier("stsTokenRedisTemplate") RedisTemplate<String, String> redisTemplate,
                               ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * 生成缓存键
     * 基于IP地址和请求参数生成唯一的缓存键
     * 
     * @param clientIp 客户端IP
     * @param roleArn 角色ARN
     * @param sessionName 会话名称
     * @param policy 策略内容
     * @param durationSeconds 持续时间
     * @return 缓存键
     */
    public String generateCacheKey(String clientIp, String roleArn, String sessionName, 
                                  String policy, Long durationSeconds) {
        try {
            // 构建缓存键的原始字符串
            StringBuilder keyBuilder = new StringBuilder();
            keyBuilder.append(clientIp).append(":");
            keyBuilder.append(roleArn != null ? roleArn : "").append(":");
            keyBuilder.append(sessionName != null ? sessionName : "").append(":");
            keyBuilder.append(policy != null ? policy : "").append(":");
            keyBuilder.append(durationSeconds != null ? durationSeconds : 3600L);
            
            String rawKey = keyBuilder.toString();
            
            // 使用MD5哈希生成固定长度的键，避免键过长
            String hashedKey = md5Hash(rawKey);
            
            String cacheKey = CACHE_PREFIX + CACHE_VERSION + ":" + hashedKey;
            log.debug("Generated cache key: {} for IP: {}", cacheKey, clientIp);
            
            return cacheKey;
            
        } catch (Exception e) {
            log.error("Failed to generate cache key for IP: {}", clientIp, e);
            // 如果生成失败，返回基于IP的简单键
            return CACHE_PREFIX + CACHE_VERSION + ":fallback:" + clientIp.replaceAll("[^a-zA-Z0-9]", "_");
        }
    }

    /**
     * 从缓存获取STS Token
     * 检查Token的Expiration时间，如果已过期则返回null
     *
     * @param cacheKey 缓存键
     * @return STS Token响应，如果不存在或已过期返回null
     */
    public StsTokenResponse getFromCache(String cacheKey) {
        try {
            log.debug("Attempting to get STS token from cache with key: {}", cacheKey);

            String cachedValue = redisTemplate.opsForValue().get(cacheKey);

            if (!StringUtils.hasText(cachedValue)) {
                log.debug("Cache miss for key: {}", cacheKey);
                return null;
            }

            StsTokenResponse response = objectMapper.readValue(cachedValue, StsTokenResponse.class);

            // 检查Token的过期时间
            if (isTokenExpired(response)) {
                log.info("Cached token expired for key: {} - Expiration: {}, Current time: {}",
                        cacheKey, response.getExpiration(), LocalDateTime.now());

                // 删除过期的缓存
                evictFromCache(cacheKey);
                return null;
            }

            log.info("Cache hit for key: {} - Retrieved valid STS token from cache, expires at: {}",
                    cacheKey, response.getExpiration());

            return response;

        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize cached STS token for key: {}", cacheKey, e);
            // 删除损坏的缓存
            redisTemplate.delete(cacheKey);
            return null;
        } catch (Exception e) {
            log.error("Failed to get STS token from cache for key: {}", cacheKey, e);
            return null;
        }
    }

    /**
     * 将STS Token存入缓存
     * 
     * @param cacheKey 缓存键
     * @param response STS Token响应
     * @param cacheDuration 缓存持续时间，如果为null则使用默认时间
     */
    public void putToCache(String cacheKey, StsTokenResponse response, Duration cacheDuration) {
        try {
            log.debug("Attempting to cache STS token with key: {}", cacheKey);
            
            String jsonValue = objectMapper.writeValueAsString(response);
            Duration actualDuration = cacheDuration != null ? cacheDuration : DEFAULT_CACHE_DURATION;
            
            redisTemplate.opsForValue().set(cacheKey, jsonValue, actualDuration.toSeconds(), TimeUnit.SECONDS);
            
            log.info("Successfully cached STS token with key: {} for {} seconds", 
                    cacheKey, actualDuration.toSeconds());
            
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize STS token for caching with key: {}", cacheKey, e);
        } catch (Exception e) {
            log.error("Failed to cache STS token with key: {}", cacheKey, e);
        }
    }

    /**
     * 使用默认缓存时间存储
     * 
     * @param cacheKey 缓存键
     * @param response STS Token响应
     */
    public void putToCache(String cacheKey, StsTokenResponse response) {
        putToCache(cacheKey, response, null);
    }

    /**
     * 删除缓存
     * 
     * @param cacheKey 缓存键
     */
    public void evictFromCache(String cacheKey) {
        try {
            Boolean deleted = redisTemplate.delete(cacheKey);
            log.info("Evicted cache for key: {} - Success: {}", cacheKey, deleted);
        } catch (Exception e) {
            log.error("Failed to evict cache for key: {}", cacheKey, e);
        }
    }

    /**
     * 检查缓存是否存在
     * 
     * @param cacheKey 缓存键
     * @return 是否存在
     */
    public boolean existsInCache(String cacheKey) {
        try {
            Boolean exists = redisTemplate.hasKey(cacheKey);
            return exists != null && exists;
        } catch (Exception e) {
            log.error("Failed to check cache existence for key: {}", cacheKey, e);
            return false;
        }
    }

    /**
     * 获取缓存剩余过期时间
     * 
     * @param cacheKey 缓存键
     * @return 剩余秒数，-1表示永不过期，-2表示键不存在
     */
    public long getCacheExpire(String cacheKey) {
        try {
            return redisTemplate.getExpire(cacheKey, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Failed to get cache expire time for key: {}", cacheKey, e);
            return -2;
        }
    }

    /**
     * 生成MD5哈希
     * 
     * @param input 输入字符串
     * @return MD5哈希值
     */
    private String md5Hash(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5 algorithm not available", e);
            // 如果MD5不可用，使用简单的哈希码
            return String.valueOf(Math.abs(input.hashCode()));
        }
    }

    /**
     * 获取缓存统计信息
     * 
     * @param clientIp 客户端IP
     * @return 缓存统计信息
     */
    public String getCacheStats(String clientIp) {
        try {
            // 获取该IP相关的所有缓存键
            String pattern = CACHE_PREFIX + CACHE_VERSION + ":*";
            // 注意：在生产环境中，keys命令可能影响性能，建议使用scan
            
            return String.format("Cache stats for IP %s - Pattern: %s", clientIp, pattern);
        } catch (Exception e) {
            log.error("Failed to get cache stats for IP: {}", clientIp, e);
            return "Cache stats unavailable";
        }
    }

    /**
     * 检查STS Token是否已过期
     *
     * @param response STS Token响应
     * @return 是否已过期
     */
    private boolean isTokenExpired(StsTokenResponse response) {
        if (response == null || !StringUtils.hasText(response.getExpiration())) {
            log.warn("Token response or expiration is null/empty");
            return true;
        }

        try {
            // 解析过期时间，支持多种格式
            LocalDateTime expirationTime = parseExpirationTime(response.getExpiration());
            LocalDateTime currentTime = LocalDateTime.now();

            boolean expired = currentTime.isAfter(expirationTime);

            if (expired) {
                log.info("Token expired - Current: {}, Expiration: {}, Expired by: {} seconds",
                        currentTime, expirationTime,
                        java.time.Duration.between(expirationTime, currentTime).getSeconds());
            } else {
                long remainingSeconds = java.time.Duration.between(currentTime, expirationTime).getSeconds();
                log.debug("Token still valid - Remaining time: {} seconds", remainingSeconds);
            }

            return expired;

        } catch (Exception e) {
            log.error("Failed to parse expiration time: {}", response.getExpiration(), e);
            // 如果无法解析过期时间，认为已过期，强制刷新
            return true;
        }
    }

    /**
     * 解析过期时间，支持多种格式
     *
     * @param expirationStr 过期时间字符串
     * @return 解析后的LocalDateTime
     */
    private LocalDateTime parseExpirationTime(String expirationStr) {
        if (!StringUtils.hasText(expirationStr)) {
            throw new IllegalArgumentException("Expiration string is null or empty");
        }

        // 常见的时间格式
        String[] patterns = {
            "yyyy-MM-dd'T'HH:mm:ss'Z'",           // 2024-01-01T12:00:00Z
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",       // 2024-01-01T12:00:00.123Z
            "yyyy-MM-dd'T'HH:mm:ss",              // 2024-01-01T12:00:00
            "yyyy-MM-dd'T'HH:mm:ss.SSS",          // 2024-01-01T12:00:00.123
            "yyyy-MM-dd HH:mm:ss",                // 2024-01-01 12:00:00
            "yyyy-MM-dd HH:mm:ss.SSS"             // 2024-01-01 12:00:00.123
        };

        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                return LocalDateTime.parse(expirationStr, formatter);
            } catch (DateTimeParseException e) {
                log.debug("Failed to parse expiration time with pattern {}: {}", pattern, expirationStr);
                // 继续尝试下一个格式
            }
        }

        throw new IllegalArgumentException("Unable to parse expiration time: " + expirationStr);
    }

    /**
     * 获取Token剩余有效时间（秒）
     *
     * @param response STS Token响应
     * @return 剩余秒数，如果已过期返回0，解析失败返回-1
     */
    public long getTokenRemainingTime(StsTokenResponse response) {
        if (response == null || !StringUtils.hasText(response.getExpiration())) {
            return -1;
        }

        try {
            LocalDateTime expirationTime = parseExpirationTime(response.getExpiration());
            LocalDateTime currentTime = LocalDateTime.now();

            if (currentTime.isAfter(expirationTime)) {
                return 0; // 已过期
            }

            return java.time.Duration.between(currentTime, expirationTime).getSeconds();

        } catch (Exception e) {
            log.error("Failed to calculate remaining time for token: {}", response.getExpiration(), e);
            return -1;
        }
    }

    /**
     * 检查缓存中的Token是否即将过期（默认5分钟内）
     *
     * @param cacheKey 缓存键
     * @param thresholdMinutes 过期阈值（分钟）
     * @return 是否即将过期
     */
    public boolean isTokenExpiringSoon(String cacheKey, int thresholdMinutes) {
        try {
            StsTokenResponse response = getFromCacheWithoutExpirationCheck(cacheKey);
            if (response == null) {
                return true; // 缓存不存在，认为需要刷新
            }

            long remainingSeconds = getTokenRemainingTime(response);
            if (remainingSeconds <= 0) {
                return true; // 已过期或解析失败
            }

            long thresholdSeconds = thresholdMinutes * 60L;
            boolean expiringSoon = remainingSeconds <= thresholdSeconds;

            if (expiringSoon) {
                log.info("Token expiring soon for key: {} - Remaining: {} seconds, Threshold: {} seconds",
                        cacheKey, remainingSeconds, thresholdSeconds);
            }

            return expiringSoon;

        } catch (Exception e) {
            log.error("Failed to check if token is expiring soon for key: {}", cacheKey, e);
            return true; // 出错时认为需要刷新
        }
    }

    /**
     * 从缓存获取Token，不检查过期时间（内部使用）
     *
     * @param cacheKey 缓存键
     * @return STS Token响应
     */
    private StsTokenResponse getFromCacheWithoutExpirationCheck(String cacheKey) {
        try {
            String cachedValue = redisTemplate.opsForValue().get(cacheKey);
            if (!StringUtils.hasText(cachedValue)) {
                return null;
            }
            return objectMapper.readValue(cachedValue, StsTokenResponse.class);
        } catch (Exception e) {
            log.error("Failed to get token from cache without expiration check: {}", cacheKey, e);
            return null;
        }
    }
}
