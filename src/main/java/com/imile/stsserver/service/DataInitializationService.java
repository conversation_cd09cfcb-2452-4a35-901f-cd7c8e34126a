package com.imile.stsserver.service;

import com.imile.stsserver.entity.AliyunAk;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 数据初始化服务
 * 在应用启动时初始化默认的阿里云配置数据
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "app.data-initialization.enabled", havingValue = "true", matchIfMissing = false)
public class DataInitializationService implements CommandLineRunner {

    private final AliyunAkService aliyunAkService;

    public DataInitializationService(AliyunAkService aliyunAkService) {
        this.aliyunAkService = aliyunAkService;
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("=== Data Initialization Started ===");
        
        try {
            // 检查是否已有启用的配置
            int enabledCount = aliyunAkService.getEnabledCount();
            if (enabledCount > 0) {
                log.info("Found {} enabled Aliyun AK configurations, skipping initialization", enabledCount);
                return;
            }
            
            log.info("No enabled Aliyun AK configurations found, initializing default data...");
            
            // 创建默认配置
            AliyunAk defaultConfig = new AliyunAk();

            // 设置明文数据（将被自动AES加密）
            defaultConfig.setAccessKeyId("LT9EjV5mx2j8NQSVsQ");
            defaultConfig.setAccessKeySecret("5WwWBZWPn59bt8XXPl8");
            defaultConfig.setRoleArn("acs:ram::1653466991022848:role/ramslsuser");
            defaultConfig.setDescription("默认阿里云STS配置（AES加密存储）");
            defaultConfig.setStatus(1);

            // 创建配置（会自动AES加密）
            AliyunAk created = aliyunAkService.create(defaultConfig);
            
            log.info("Successfully initialized default Aliyun AK configuration:");
            log.info("  ID: {}", created.getId());
            log.info("  AccessKeyID: {}", created.getMaskedAccessKeyId());
            log.info("  RoleArn: {}", created.getMaskedRoleArn());
            log.info("  Status: {}", created.getStatus());
            log.info("  Encrypted Data Valid: {}", created.isEncryptedDataValid());
            
            log.info("=== Data Initialization Completed Successfully ===");
            
        } catch (Exception e) {
            log.error("=== Data Initialization Failed ===");
            log.error("Failed to initialize default Aliyun AK configuration", e);
            
            // 不抛出异常，避免影响应用启动
            log.warn("Application will continue without default configuration. " +
                    "Please manually create Aliyun AK configuration via API.");
        }
    }

    /**
     * 手动初始化数据（用于测试或重新初始化）
     */
    public void initializeData() {
        log.info("=== Manual Data Initialization Started ===");
        
        try {
            // 创建测试配置
            AliyunAk testConfig = new AliyunAk();
            testConfig.setAccessKeyId("LTAI5tKz9EjV5mx2j8NQSVsQ");
            testConfig.setAccessKeySecret("******************************");
            testConfig.setRoleArn("acs:ram::1653466991022848:role/ramslsuser");
            testConfig.setDescription("手动初始化的测试配置");
            testConfig.setStatus(1);
            
            AliyunAk created = aliyunAkService.create(testConfig);
            
            log.info("Manual initialization completed - ID: {}, AccessKeyID: {}", 
                    created.getId(), created.getMaskedAccessKeyId());
            
        } catch (Exception e) {
            log.error("Manual data initialization failed", e);
            throw new RuntimeException("Failed to initialize data manually", e);
        }
    }

    /**
     * 验证现有配置的完整性
     */
    public void validateExistingConfigurations() {
        log.info("=== Validating Existing Configurations ===");
        
        try {
            var allConfigs = aliyunAkService.getAll();
            
            for (AliyunAk config : allConfigs) {
                boolean isValid = config.isEncryptedDataValid();
                log.info("Configuration ID: {} - Valid: {}, Status: {}, Description: {}", 
                        config.getId(), isValid, config.getStatus(), config.getDescription());
                
                if (!isValid) {
                    log.warn("Invalid encrypted data found for configuration ID: {}", config.getId());
                }
            }
            
            log.info("Configuration validation completed - Total: {}, Enabled: {}", 
                    allConfigs.size(), aliyunAkService.getEnabledCount());
            
        } catch (Exception e) {
            log.error("Failed to validate existing configurations", e);
        }
    }

    /**
     * 清理无效配置
     */
    public void cleanupInvalidConfigurations() {
        log.info("=== Cleaning Up Invalid Configurations ===");
        
        try {
            var allConfigs = aliyunAkService.getAll();
            int cleanedCount = 0;
            
            for (AliyunAk config : allConfigs) {
                if (!config.isEncryptedDataValid()) {
                    log.warn("Deleting invalid configuration - ID: {}, Description: {}", 
                            config.getId(), config.getDescription());
                    
                    boolean deleted = aliyunAkService.delete(config.getId());
                    if (deleted) {
                        cleanedCount++;
                        log.info("Successfully deleted invalid configuration ID: {}", config.getId());
                    } else {
                        log.error("Failed to delete invalid configuration ID: {}", config.getId());
                    }
                }
            }
            
            log.info("Cleanup completed - Deleted {} invalid configurations", cleanedCount);
            
        } catch (Exception e) {
            log.error("Failed to cleanup invalid configurations", e);
        }
    }
}
