package com.imile.stsserver.interceptor;

import com.imile.stsserver.util.LogUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * TraceID 拦截器
 * 为每个HTTP请求自动生成或传递TraceID
 */
@Slf4j
@Component
public class TraceIdInterceptor implements HandlerInterceptor {

    private static final String TRACE_ID_HEADER = "X-Trace-ID";
    private static final String TRACE_ID_HEADER_ALT = "EagleEye-TraceID";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 尝试从请求头获取TraceID
        String traceId = getTraceIdFromRequest(request);
        boolean traceIdFromRequest = StringUtils.hasText(traceId);

        // 如果没有TraceID，生成一个新的
        if (!traceIdFromRequest) {
            traceId = LogUtil.generateTraceId();
        }

        // 设置TraceID到MDC
        LogUtil.setTraceId(traceId);

        // 将TraceID添加到响应头
        response.setHeader(TRACE_ID_HEADER, traceId);

        // 记录详细的请求信息
        log.info("=== HTTP Request Started ===");
        log.info("Request Details - Method: {}, URI: {}, TraceID: {} ({})",
                request.getMethod(),
                request.getRequestURI(),
                traceId,
                traceIdFromRequest ? "From Request" : "Generated");

        // 记录请求头信息（排除敏感信息）
        log.info("Request Headers:");
        request.getHeaderNames().asIterator().forEachRemaining(headerName -> {
            if (!headerName.toLowerCase().contains("secret") &&
                !headerName.toLowerCase().contains("password") &&
                !headerName.toLowerCase().contains("token")) {
                log.info("  {}: {}", headerName, request.getHeader(headerName));
            } else {
                log.info("  {}: ***MASKED***", headerName);
            }
        });

        // 记录请求参数
        if (!request.getParameterMap().isEmpty()) {
            log.info("Request Parameters:");
            request.getParameterMap().forEach((key, values) -> {
                if (!key.toLowerCase().contains("secret") &&
                    !key.toLowerCase().contains("password")) {
                    log.info("  {}: {}", key, String.join(", ", values));
                } else {
                    log.info("  {}: ***MASKED***", key);
                }
            });
        }

        // 记录客户端信息
        log.info("Client Info - RemoteAddr: {}, UserAgent: {}",
                request.getRemoteAddr(),
                request.getHeader("User-Agent"));

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                               Object handler, Exception ex) {
        try {
            String traceId = LogUtil.getTraceId();

            // 记录请求完成信息
            log.info("=== HTTP Request Completed ===");
            log.info("Response Details - Method: {}, URI: {}, Status: {}, TraceID: {}",
                    request.getMethod(),
                    request.getRequestURI(),
                    response.getStatus(),
                    traceId);

            // 记录响应头信息
            log.info("Response Headers:");
            response.getHeaderNames().forEach(headerName -> {
                log.info("  {}: {}", headerName, response.getHeader(headerName));
            });

            // 记录响应状态分析
            int status = response.getStatus();
            if (status >= 200 && status < 300) {
                log.info("Request completed successfully - Status: {}", status);
            } else if (status >= 300 && status < 400) {
                log.info("Request completed with redirection - Status: {}", status);
            } else if (status >= 400 && status < 500) {
                log.warn("Request completed with client error - Status: {}", status);
            } else if (status >= 500) {
                log.error("Request completed with server error - Status: {}", status);
            }

            // 如果有异常，记录详细错误日志
            if (ex != null) {
                log.error("=== HTTP Request Failed with Exception ===");
                log.error("Exception Details - Type: {}, Message: {}, TraceID: {}",
                        ex.getClass().getSimpleName(), ex.getMessage(), traceId);
                log.error("Request that caused exception - Method: {}, URI: {}",
                        request.getMethod(), request.getRequestURI());
                log.error("Exception stack trace:", ex);
            } else {
                log.info("Request completed without exceptions");
            }

            log.info("=== HTTP Request Processing Finished - TraceID: {} ===", traceId);

        } catch (Exception e) {
            log.error("Error in afterCompletion interceptor", e);
        } finally {
            // 清除MDC
            LogUtil.clearMDC();
        }
    }

    /**
     * 从请求中获取TraceID
     * 
     * @param request HTTP请求
     * @return TraceID
     */
    private String getTraceIdFromRequest(HttpServletRequest request) {
        // 优先从标准头获取
        String traceId = request.getHeader(TRACE_ID_HEADER);
        
        // 如果没有，尝试从备用头获取
        if (!StringUtils.hasText(traceId)) {
            traceId = request.getHeader(TRACE_ID_HEADER_ALT);
        }
        
        // 如果还没有，尝试从参数获取
        if (!StringUtils.hasText(traceId)) {
            traceId = request.getParameter("traceId");
        }
        
        return traceId;
    }
}
