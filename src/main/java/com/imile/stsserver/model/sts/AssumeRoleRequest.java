package com.imile.stsserver.model.sts;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * Assume Role Request Model
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AssumeRoleRequest {

    /**
     * 认证token，必须与数据库中的AccessKeyID_hash字段匹配
     */
    @NotBlank(message = "Authentication token is required")
    private String token;

    /**
     * ARN of the role to assume（可选，如果不提供则使用数据库配置）
     */
    private String roleArn;
    
    /**
     * Session name for the assumed role
     * 如果为空，系统将自动生成随机的 session name
     */
    private String roleSessionName;
    
    /**
     * Policy document (optional)
     * 可以是：
     * 1. 文件名（如：write_policy.txt, read_policy.json）- 系统会自动读取文件内容
     * 2. 完整的JSON字符串 - 直接使用
     */
    private String policy;
    
    /**
     * Duration in seconds (900-43200, default 3600)
     * 如果不提供，默认为 3600 秒（1小时）
     */
    @Min(value = 900, message = "Duration must be at least 900 seconds")
    @Max(value = 43200, message = "Duration cannot exceed 43200 seconds")
    private Long durationSeconds = 3600L;
    
    /**
     * External ID (optional)
     */
    private String externalId;
}
