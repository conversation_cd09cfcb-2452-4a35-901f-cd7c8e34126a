package com.imile.stsserver.model.sts;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * STS Token Response Model - 阿里云官方格式
 * 返回格式与阿里云 STS 接口保持一致
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StsTokenResponse {
    
    /**
     * 状态码
     */
    @JsonProperty("StatusCode")
    private String statusCode = "200";
    
    /**
     * 临时访问密钥ID
     */
    @JsonProperty("AccessKeyId")
    private String accessKeyId;
    
    /**
     * 临时访问密钥Secret
     */
    @JsonProperty("AccessKeySecret")
    private String accessKeySecret;
    
    /**
     * 安全令牌
     */
    @JsonProperty("SecurityToken")
    private String securityToken;
    
    /**
     * 过期时间 (ISO 8601格式)
     */
    @JsonProperty("Expiration")
    private String expiration;

    /**
     * 过期时间戳 (Unix时间戳，秒)
     */
    @JsonProperty("ExpirationTimestamp")
    private Long expirationTimestamp;

    /**
     * 请求开始时间 (ISO 8601格式)
     */
    @JsonProperty("RequestStartTime")
    private String requestStartTime;

    /**
     * 请求开始时间戳 (Unix时间戳，秒)
     */
    @JsonProperty("RequestStartTimestamp")
    private Long requestStartTimestamp;

    /**
     * 错误码 (仅在出错时返回)
     */
    @JsonProperty("ErrorCode")
    private String errorCode;
    
    /**
     * 错误信息 (仅在出错时返回)
     */
    @JsonProperty("ErrorMessage")
    private String errorMessage;
    
    /**
     * 请求ID (仅在出错时返回)
     */
    @JsonProperty("RequestId")
    private String requestId;
}
