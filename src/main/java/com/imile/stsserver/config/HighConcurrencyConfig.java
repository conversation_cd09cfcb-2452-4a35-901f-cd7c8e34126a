package com.imile.stsserver.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 高并发配置类
 * 优化线程池配置以支持每秒1万并发
 */
@Slf4j
@Configuration
@EnableAsync
public class HighConcurrencyConfig {

    /**
     * 配置异步任务执行器
     * 支持高并发处理
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：CPU核心数 * 2
        int corePoolSize = Runtime.getRuntime().availableProcessors() * 2;
        executor.setCorePoolSize(corePoolSize);
        
        // 最大线程数：支持高并发
        executor.setMaxPoolSize(200);
        
        // 队列容量：缓冲高峰请求
        executor.setQueueCapacity(10000);
        
        // 线程名前缀
        executor.setThreadNamePrefix("STS-Async-");
        
        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 线程空闲时间
        executor.setKeepAliveSeconds(60);
        
        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(true);
        
        // 等待任务完成后关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("High concurrency task executor initialized - CorePoolSize: {}, MaxPoolSize: {}, QueueCapacity: {}", 
                corePoolSize, 200, 10000);
        
        return executor;
    }

    /**
     * STS专用线程池
     * 专门处理STS Token请求
     */
    @Bean(name = "stsExecutor")
    public Executor stsExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 针对STS请求优化的线程池配置
        executor.setCorePoolSize(50);
        executor.setMaxPoolSize(500);
        executor.setQueueCapacity(20000);
        executor.setThreadNamePrefix("STS-Token-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setKeepAliveSeconds(30);
        executor.setAllowCoreThreadTimeOut(true);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        
        log.info("STS dedicated executor initialized - CorePoolSize: 50, MaxPoolSize: 500, QueueCapacity: 20000");
        
        return executor;
    }

    /**
     * 缓存操作专用线程池
     * 处理Redis缓存操作
     */
    @Bean(name = "cacheExecutor")
    public Executor cacheExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 缓存操作通常很快，使用较小的线程池
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(100);
        executor.setQueueCapacity(5000);
        executor.setThreadNamePrefix("Cache-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setKeepAliveSeconds(30);
        executor.setAllowCoreThreadTimeOut(true);
        
        executor.initialize();
        
        log.info("Cache executor initialized - CorePoolSize: 20, MaxPoolSize: 100, QueueCapacity: 5000");
        
        return executor;
    }

    /**
     * 数据库操作专用线程池
     * 处理数据库查询操作
     */
    @Bean(name = "dbExecutor")
    public Executor dbExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 数据库连接有限，使用适中的线程池
        executor.setCorePoolSize(30);
        executor.setMaxPoolSize(150);
        executor.setQueueCapacity(8000);
        executor.setThreadNamePrefix("DB-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setKeepAliveSeconds(60);
        executor.setAllowCoreThreadTimeOut(true);
        
        executor.initialize();
        
        log.info("Database executor initialized - CorePoolSize: 30, MaxPoolSize: 150, QueueCapacity: 8000");
        
        return executor;
    }
}
