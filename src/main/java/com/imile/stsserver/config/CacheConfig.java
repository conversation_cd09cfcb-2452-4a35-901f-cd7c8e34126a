package com.imile.stsserver.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存配置类
 */
@Slf4j
@Configuration
@EnableCaching
public class CacheConfig extends CachingConfigurerSupport {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    /**
     * 创建Redis缓存管理器
     */
    @Bean
    @Override
    public CacheManager cacheManager() {
        log.info("Initializing Redis Cache Manager");

        // 默认缓存配置 - 10分钟
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(10))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();

        // 不同缓存的特定配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

        // 策略缓存 - 10分钟
        cacheConfigurations.put("policy-cache", defaultConfig
                .prefixCacheNameWith("policy:")
                .entryTtl(Duration.ofMinutes(10)));

        // 阿里云AK缓存 - 10分钟
        cacheConfigurations.put("aliyun-ak-cache", defaultConfig
                .prefixCacheNameWith("aliyun-ak:")
                .entryTtl(Duration.ofMinutes(10)));

        // STS Token缓存 - 根据token过期时间动态设置，这里设置默认10分钟
        cacheConfigurations.put("sts-token-cache", defaultConfig
                .prefixCacheNameWith("sts-token:")
                .entryTtl(Duration.ofMinutes(10)));

        // 创建缓存管理器
        RedisCacheManager cacheManager = RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();

        log.info("Redis Cache Manager initialized with {} cache configurations", cacheConfigurations.size());
        return cacheManager;
    }

    /**
     * 自定义缓存键生成器
     */
    @Bean
    @Override
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getSimpleName()).append(":");
            sb.append(method.getName()).append(":");
            
            if (params.length > 0) {
                for (Object param : params) {
                    if (param != null) {
                        sb.append(param.toString()).append(":");
                    } else {
                        sb.append("null:");
                    }
                }
                // 移除最后一个冒号
                sb.setLength(sb.length() - 1);
            }
            
            String key = sb.toString();
            log.debug("Generated cache key: {}", key);
            return key;
        };
    }

    /**
     * 缓存错误处理器
     */
    @Bean
    @Override
    public CacheErrorHandler errorHandler() {
        return new CacheErrorHandler() {
            @Override
            public void handleCacheGetError(RuntimeException exception, org.springframework.cache.Cache cache, Object key) {
                log.error("Cache get error - Cache: {}, Key: {}", cache.getName(), key, exception);
                // 缓存获取失败时不抛出异常，继续执行业务逻辑
            }

            @Override
            public void handleCachePutError(RuntimeException exception, org.springframework.cache.Cache cache, Object key, Object value) {
                log.error("Cache put error - Cache: {}, Key: {}", cache.getName(), key, exception);
                // 缓存存储失败时不抛出异常，继续执行业务逻辑
            }

            @Override
            public void handleCacheEvictError(RuntimeException exception, org.springframework.cache.Cache cache, Object key) {
                log.error("Cache evict error - Cache: {}, Key: {}", cache.getName(), key, exception);
                // 缓存清除失败时不抛出异常，继续执行业务逻辑
            }

            @Override
            public void handleCacheClearError(RuntimeException exception, org.springframework.cache.Cache cache) {
                log.error("Cache clear error - Cache: {}", cache.getName(), exception);
                // 缓存清空失败时不抛出异常，继续执行业务逻辑
            }
        };
    }
}
