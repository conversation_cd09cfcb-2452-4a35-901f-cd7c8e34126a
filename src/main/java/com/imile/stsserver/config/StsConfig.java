package com.imile.stsserver.config;

import com.aliyun.sts20150401.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * STS Configuration for Aliyun STS Client
 */
@Configuration
public class StsConfig {

    @Value("${aliyun.sts.access-key-id:}")
    private String accessKeyId;

    @Value("${aliyun.sts.access-key-secret:}")
    private String accessKeySecret;

    @Value("${aliyun.sts.endpoint:sts.cn-hangzhou.aliyuncs.com}")
    private String endpoint;

    @Value("${aliyun.sts.region-id:cn-hangzhou}")
    private String regionId;

    /**
     * Create STS Client Bean
     * @return STS Client instance
     * @throws Exception if configuration fails
     */
    @Bean
    public Client stsClient() throws Exception {
        // Get credentials from environment variables if not set in properties
        String keyId = accessKeyId.isEmpty() ? System.getenv("ALIYUN_ACCESS_KEY_ID") : accessKeyId;
        String keySecret = accessKeySecret.isEmpty() ? System.getenv("ALIYUN_ACCESS_KEY_SECRET") : accessKeySecret;

        if (keyId == null || keyId.isEmpty() || keySecret == null || keySecret.isEmpty()) {
            throw new IllegalArgumentException("Aliyun Access Key ID and Secret must be provided either through " +
                    "application properties or environment variables (ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET)");
        }

        Config config = new Config()
                .setAccessKeyId(keyId)
                .setAccessKeySecret(keySecret)
                .setEndpoint(endpoint)
                .setRegionId(regionId);

        return new Client(config);
    }
}
