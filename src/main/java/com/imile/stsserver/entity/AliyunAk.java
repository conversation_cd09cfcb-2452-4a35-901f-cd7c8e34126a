package com.imile.stsserver.entity;

import com.imile.stsserver.util.EncryptionUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 阿里云AccessKey配置实体类
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AliyunAk {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 加密的阿里云AccessKeyID（数据库存储字段）
     */
    private String accessKeyIdEncrypted;

    /**
     * 加密的阿里云AccessKeySecret（数据库存储字段）
     */
    private String accessKeySecretEncrypted;

    /**
     * 加密的阿里云角色ARN（数据库存储字段）
     */
    private String roleArnEncrypted;

    /**
     * AccessKeyID的哈希值（用于查询，数据库存储字段）
     */
    private String accessKeyIdHash;

    /**
     * 明文AccessKeyID（临时字段，不存储到数据库）
     */
    private transient String accessKeyId;

    /**
     * 明文AccessKeySecret（临时字段，不存储到数据库）
     */
    private transient String accessKeySecret;

    /**
     * 明文RoleArn（临时字段，不存储到数据库）
     */
    private transient String roleArn;

    /**
     * 创建时间
     */
    private LocalDateTime createdate;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdDate;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 启用
     */
    public AliyunAk enable() {
        this.status = 1;
        return this;
    }

    /**
     * 禁用
     */
    public AliyunAk disable() {
        this.status = 0;
        return this;
    }

    /**
     * 设置明文数据并自动AES加密
     *
     * @param plainAccessKeyId 明文AccessKeyID
     * @param plainAccessKeySecret 明文AccessKeySecret
     * @param plainRoleArn 明文RoleArn
     */
    public void setPlainData(String plainAccessKeyId, String plainAccessKeySecret, String plainRoleArn) {
        // 设置明文字段（临时使用）
        this.accessKeyId = plainAccessKeyId;
        this.accessKeySecret = plainAccessKeySecret;
        this.roleArn = plainRoleArn;

        // AES加密并设置加密字段
        if (StringUtils.hasText(plainAccessKeyId)) {
            this.accessKeyIdEncrypted = EncryptionUtil.encryptAES(plainAccessKeyId);
            this.accessKeyIdHash = EncryptionUtil.generateMD5Hash(plainAccessKeyId); // 哈希仍用MD5，用于查询
        }
        if (StringUtils.hasText(plainAccessKeySecret)) {
            this.accessKeySecretEncrypted = EncryptionUtil.encryptAES(plainAccessKeySecret);
        }
        if (StringUtils.hasText(plainRoleArn)) {
            this.roleArnEncrypted = EncryptionUtil.encryptAES(plainRoleArn);
        }
    }

    /**
     * 从加密数据解密获取明文数据
     *
     * @return 解密后的明文数据数组 [accessKeyId, accessKeySecret, roleArn]
     */
    public String[] decryptToPlainData() {
        try {
            String[] decrypted = EncryptionUtil.decryptAliyunConfigAES(
                    this.accessKeyIdEncrypted,
                    this.accessKeySecretEncrypted,
                    this.roleArnEncrypted
            );

            // 设置明文字段（临时使用）
            this.accessKeyId = decrypted[0];
            this.accessKeySecret = decrypted[1];
            this.roleArn = decrypted[2];

            log.info("Successfully decrypted Aliyun AK configuration - ID: {}, AccessKeyID: {}",
                    this.id, getMaskedAccessKeyId());

            return decrypted;

        } catch (Exception e) {
            log.error("Failed to decrypt Aliyun AK configuration - ID: {}", this.id, e);
            throw new RuntimeException("Failed to decrypt configuration", e);
        }
    }

    /**
     * 验证明文数据是否与加密数据匹配（使用AES验证）
     *
     * @param plainAccessKeyId 明文AccessKeyID
     * @param plainAccessKeySecret 明文AccessKeySecret
     * @param plainRoleArn 明文RoleArn
     * @return 是否匹配
     */
    public boolean verifyPlainData(String plainAccessKeyId, String plainAccessKeySecret, String plainRoleArn) {
        return EncryptionUtil.verifyAES(plainAccessKeyId, this.accessKeyIdEncrypted) &&
               EncryptionUtil.verifyAES(plainAccessKeySecret, this.accessKeySecretEncrypted) &&
               EncryptionUtil.verifyAES(plainRoleArn, this.roleArnEncrypted);
    }

    /**
     * 验证AccessKeyID是否匹配（使用AES验证）
     *
     * @param plainAccessKeyId 明文AccessKeyID
     * @return 是否匹配
     */
    public boolean verifyAccessKeyId(String plainAccessKeyId) {
        return EncryptionUtil.verifyAES(plainAccessKeyId, this.accessKeyIdEncrypted);
    }

    /**
     * 获取脱敏后的AccessKeySecret（用于日志输出）
     */
    public String getMaskedAccessKeySecret() {
        if (StringUtils.hasText(accessKeySecret)) {
            return EncryptionUtil.maskSensitiveData(accessKeySecret, 4);
        }
        return EncryptionUtil.getEncryptedDataSummary(accessKeySecretEncrypted);
    }

    /**
     * 获取脱敏后的AccessKeyID（用于日志输出）
     */
    public String getMaskedAccessKeyId() {
        if (StringUtils.hasText(accessKeyId)) {
            return EncryptionUtil.maskSensitiveData(accessKeyId, 8);
        }
        return EncryptionUtil.getEncryptedDataSummary(accessKeyIdEncrypted);
    }

    /**
     * 获取脱敏后的RoleArn（用于日志输出）
     */
    public String getMaskedRoleArn() {
        if (StringUtils.hasText(roleArn)) {
            return EncryptionUtil.maskSensitiveData(roleArn, 10);
        }
        return EncryptionUtil.getEncryptedDataSummary(roleArnEncrypted);
    }

    /**
     * 检查AES加密数据的完整性
     *
     * @return 是否完整
     */
    public boolean isEncryptedDataValid() {
        return EncryptionUtil.isValidAESEncryptedData(accessKeyIdEncrypted) &&
               EncryptionUtil.isValidAESEncryptedData(accessKeySecretEncrypted) &&
               EncryptionUtil.isValidAESEncryptedData(roleArnEncrypted) &&
               StringUtils.hasText(accessKeyIdHash);
    }

    /**
     * 清除明文数据（安全考虑）
     */
    public void clearPlainData() {
        this.accessKeyId = null;
        this.accessKeySecret = null;
        this.roleArn = null;
    }

    @Override
    public String toString() {
        return "AliyunAk{" +
                "id=" + id +
                ", accessKeyId='" + getMaskedAccessKeyId() + '\'' +
                ", accessKeySecret='" + getMaskedAccessKeySecret() + '\'' +
                ", roleArn='" + getMaskedRoleArn() + '\'' +
                ", accessKeyIdHash='" + (accessKeyIdHash != null ? accessKeyIdHash.substring(0, Math.min(8, accessKeyIdHash.length())) + "***" : "null") + '\'' +
                ", createdate=" + createdate +
                ", lastUpdDate=" + lastUpdDate +
                ", status=" + status +
                ", description='" + description + '\'' +
                ", encryptedDataValid=" + isEncryptedDataValid() +
                '}';
    }
}
