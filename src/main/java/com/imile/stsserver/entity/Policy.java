package com.imile.stsserver.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 策略实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Policy {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 策略名称（唯一标识）
     */
    private String name;

    /**
     * 策略显示名称
     */
    private String displayName;

    /**
     * 策略内容（JSON格式）
     */
    private String content;

    /**
     * 策略描述
     */
    private String description;

    /**
     * 策略类型（如：OSS, LOG等）
     */
    private String type;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdDate;

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 启用策略
     */
    public Policy enable() {
        this.status = 1;
        return this;
    }

    /**
     * 禁用策略
     */
    public Policy disable() {
        this.status = 0;
        return this;
    }

    /**
     * 验证策略内容是否为有效的JSON格式
     */
    public boolean isValidJson() {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        
        try {
            String trimmed = content.trim();
            return trimmed.startsWith("{") && 
                   trimmed.endsWith("}") && 
                   trimmed.contains("Statement") && 
                   trimmed.contains("Version");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取格式化的策略内容
     */
    public String getFormattedContent() {
        if (content == null) {
            return null;
        }
        // 移除多余的空格和换行，但保持JSON结构
        return content.replaceAll("\\s+", " ").trim();
    }
}
