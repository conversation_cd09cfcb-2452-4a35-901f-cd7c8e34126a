server:
  port: 80
spring:
  application:
    name: sts-server
  datasource:
    url: **************************************************************************************************************************************
    username: sts_server_rw
    password: wDvK&k4&&iC
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      # 连接池名称
      pool-name: StsServerHikariCP
      # 最小空闲连接数
      minimum-idle: 10
      # 最大连接池大小
      maximum-pool-size: 50
      # 连接超时时间（毫秒）
      connection-timeout: 30000
      # 空闲连接超时时间（毫秒）- 10分钟
      idle-timeout: 600000
      # 连接最大生命周期（毫秒）- 30分钟
      max-lifetime: 1800000
      # 连接测试查询
      connection-test-query: SELECT 1
      # 是否自动提交
      auto-commit: true
      # 连接初始化SQL
      connection-init-sql: SET NAMES utf8mb4
      # 验证超时时间（毫秒）
      validation-timeout: 5000
      # 泄漏检测阈值（毫秒）- 60秒，用于检测连接泄漏
      leak-detection-threshold: 60000
  data:
    redis:
      host: **********
      port: 6379
      password: HFVlCMgdblAmBiO
      database: 0
      timeout: 1000ms      # 优化：减少超时时间
      connect-timeout: 1000ms
      lettuce:
        pool:
          max-active: 500    # 优化：支持高并发，增加到500
          max-idle: 50       # 优化：增加空闲连接数
          min-idle: 20       # 优化：增加最小空闲连接数
          max-wait: 500ms    # 优化：减少等待时间
          time-between-eviction-runs: 30s
        shutdown-timeout: 100ms
  cache:
    type: redis
    redis:
      key-prefix: sts-cache
      time-to-live: 600000  # 10分钟缓存 (600秒 * 1000毫秒)
      use-key-prefix: true
      cache-null-values: false  # 不缓存null值
    cache-names:
      - policy-cache
      - aliyun-ak-cache
mybatis:
  type-aliases-package: com.imile.stsserver.entity
  mapper-locations: classpath:mappers/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# Aliyun STS Configuration
aliyun:
  sts:
    # 阿里云访问密钥配置
    access-key-id: LT9EjV5mx2jSVsQ
    access-key-secret: 5WwWBZWPbt8XXPl8
    endpoint: sts.cn-hangzhou.aliyuncs.com
    region-id: cn-hangzhou
    default-duration-seconds: 3600
    max-duration-seconds: 43200
    min-duration-seconds: 900
    # 认证令牌配置 - 简单的基于令牌的认证
    tokens:
      demo-token-123: default  # 示例令牌
      test-token-456: default  # 测试令牌

# Logging configuration
logging:
  config: classpath:logback-spring.xml
  level:
    root: INFO
    com.imile.stsserver: DEBUG
  file:
    path: logs
  pattern:
    dateformat: yyyy-MM-dd HH:mm:ss.SSS



# STS Token缓存配置
cache:
  sts-token:
    default-ttl: 600  # 默认缓存时间（秒）- 10分钟
    max-entries: 10000 # 最大缓存条目数

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# Application info
info:
  app:
    name: STSServer
    description: Aliyun STS Token Server
    version: 1.0.0
    author: Demo5 Team

app:
  data-initialization:
    enabled: false  # 是否在应用启动时初始化默认的阿里云配置数据
  policy-initialization:
    enabled: false   # 是否在应用启动时初始化默认的策略数据
