<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义变量 -->
    <springProfile name="!prod">
        <property name="LOG_HOME" value="logs"/>
    </springProfile>
    <springProfile name="prod">
        <property name="LOG_HOME" value="/var/log/app"/>
    </springProfile>

    <property name="LOG_DATEFORMAT_PATTERN" value="yyyy-MM-dd HH:mm:ss.SSS"/>
    <property name="springAppName" value="AppTokenServer"/>

    <!-- 获取本机IP地址 -->
    <property name="ip" value="${HOSTNAME:-localhost}"/>
    
    <!-- JSON格式日志模式 -->
    <property name="JSON_PATTERN" value='{
        "time": "%d{${LOG_DATEFORMAT_PATTERN}}",
        "ip": "${HOSTNAME:-unknown}",
        "level": "%level",
        "thread": "%thread",
        "stack_trace": "%ex{full}",
        "class": "%c",
        "line": "%L",
        "traceId": "%X{EagleEye-TraceID:-}",
        "appName": "${springAppName}",
        "message": "%msg"
    }'/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- INFO级别日志文件 -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/info.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>{"time":"%d{${LOG_DATEFORMAT_PATTERN}}","ip":"${ip}","level":"%level","thread":"%thread","stack_trace":"%replace(%ex{full}){'\"','\\\"'}","class":"%c","line":"%L","traceId":"%X{EagleEye-TraceID:-}","appName":"${springAppName}","message":"%replace(%msg){'\"','\\\"'}"}%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        
        <!-- 滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        
        <!-- 过滤器：只记录INFO及以上级别，但排除ERROR -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <!-- ERROR级别日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/error.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>{"time":"%d{${LOG_DATEFORMAT_PATTERN}}","ip":"${ip}","level":"%level","thread":"%thread","stack_trace":"%replace(%ex{full}){'\"','\\\"'}","class":"%c","line":"%L","traceId":"%X{EagleEye-TraceID:-}","appName":"${springAppName}","message":"%replace(%msg){'\"','\\\"'}"}%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        
        <!-- 滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        
        <!-- 过滤器：只记录ERROR级别 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 异步日志 -->
    <appender name="ASYNC_INFO" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="INFO_FILE"/>
    </appender>

    <appender name="ASYNC_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="ERROR_FILE"/>
    </appender>

    <!-- 特定包的日志级别 -->
    <logger name="com.imile.demo5" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_INFO"/>
        <appender-ref ref="ASYNC_ERROR"/>
    </logger>

    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_INFO"/>
        <appender-ref ref="ASYNC_ERROR"/>
    </root>

</configuration>
