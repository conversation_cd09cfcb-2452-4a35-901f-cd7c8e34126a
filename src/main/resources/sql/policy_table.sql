-- 创建策略表
CREATE TABLE IF NOT EXISTS `policy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '策略名称（唯一标识）',
  `display_name` varchar(200) DEFAULT NULL COMMENT '策略显示名称',
  `content` text NOT NULL COMMENT '策略内容（JSON格式）',
  `description` varchar(500) DEFAULT NULL COMMENT '策略描述',
  `type` varchar(50) DEFAULT NULL COMMENT '策略类型（如：OSS, LOG等）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_upd_date` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_policy_name` (`name`),
  KEY `idx_policy_status` (`status`),
  KEY `idx_policy_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='策略表';

-- 插入默认策略数据
INSERT INTO `policy` (`name`, `display_name`, `content`, `description`, `type`, `status`) VALUES
('oss_read_policy', 'OSS Read Policy', '{
  "Statement": [
    {
      "Action": [
        "oss:GetObject",
        "oss:ListObjects"
      ],
      "Effect": "Allow",
      "Resource": ["acs:oss:*:*:*"]
    }
  ],
  "Version": "1"
}', 'OSS读取权限策略', 'OSS', 1),

('oss_write_policy', 'OSS Write Policy', '{
  "Statement": [
    {
      "Action": [
        "oss:PutObject",
        "oss:DeleteObject"
      ],
      "Effect": "Allow",
      "Resource": ["acs:oss:*:*:*"]
    }
  ],
  "Version": "1"
}', 'OSS写入权限策略', 'OSS', 1),

('read_policy', 'Log Read Policy', '{
  "Statement": [
    {
      "Action": [
        "log:GetLogStoreLogs",
        "log:ListLogStores",
        "log:GetIndex"
      ],
      "Effect": "Allow",
      "Resource": ["acs:log:*:*:*"]
    }
  ],
  "Version": "1"
}', '日志读取权限策略', 'LOG', 1),

('write_policy', 'Log Write Policy', '{
  "Statement": [
    {
      "Action": [
        "log:PostLogStoreLogs"
      ],
      "Effect": "Allow",
      "Resource": ["acs:log:*:*:*"]
    }
  ],
  "Version": "1"
}', '日志写入权限策略', 'LOG', 1);
