# STS Token 响应时间格式统一说明

## 时间字段格式统一

在 `/api/sts/token` 接口的响应中，所有时间相关字段现在都使用统一的格式：

### ISO 8601 时间格式
- **格式**: `yyyy-MM-dd'T'HH:mm:ss'Z'`
- **示例**: `2024-01-15T10:30:45Z`
- **字段**: 
  - `Expiration` - Token过期时间
  - `RequestStartTime` - 请求开始时间

### Unix 时间戳格式
- **格式**: 秒级时间戳（10位数字）
- **示例**: `1705315845`
- **字段**:
  - `ExpirationTimestamp` - Token过期时间戳
  - `RequestStartTimestamp` - 请求开始时间戳

## 响应示例

```json
{
  "StatusCode": "200",
  "AccessKeyId": "STS.NUgYrLnoC***",
  "AccessKeySecret": "AzQNq6VW***",
  "SecurityToken": "CAIS8wF1q6***",
  "Expiration": "2024-01-15T11:30:45Z",
  "ExpirationTimestamp": 1705319445,
  "RequestStartTime": "2024-01-15T10:30:45Z",
  "RequestStartTimestamp": 1705315845
}
```

## 时间字段说明

| 字段名 | 格式 | 单位 | 说明 |
|--------|------|------|------|
| `Expiration` | ISO 8601 | - | Token过期时间，便于人类阅读 |
| `ExpirationTimestamp` | Unix时间戳 | 秒 | Token过期时间戳，便于程序处理 |
| `RequestStartTime` | ISO 8601 | - | 请求开始时间，便于人类阅读 |
| `RequestStartTimestamp` | Unix时间戳 | 秒 | 请求开始时间戳，便于程序处理 |

## 使用建议

1. **人类阅读**: 使用 ISO 8601 格式的字段（`Expiration`, `RequestStartTime`）
2. **程序处理**: 使用时间戳字段（`ExpirationTimestamp`, `RequestStartTimestamp`）
3. **时间计算**: 使用时间戳进行数学运算更加方便

## 时间计算示例

```javascript
// 计算Token剩余有效时间（秒）
const remainingSeconds = response.ExpirationTimestamp - Math.floor(Date.now() / 1000);

// 计算请求处理时间（如果需要）
const requestDuration = response.ExpirationTimestamp - response.RequestStartTimestamp;
```

## 注意事项

1. 所有时间戳都是UTC时间
2. ISO 8601格式以'Z'结尾表示UTC时区
3. 时间戳统一使用秒级精度，保持一致性
4. 缓存命中和缓存未命中的响应格式完全一致
