# 策略管理功能使用说明

## 概述

系统现在支持将策略存储在数据库中，而不是从文件系统读取。策略可以通过API进行管理，并且在STS Token请求中可以通过策略名称或直接传入JSON来使用。

## 数据库表结构

### policy表
```sql
CREATE TABLE `policy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '策略名称（唯一标识）',
  `display_name` varchar(200) DEFAULT NULL COMMENT '策略显示名称',
  `content` text NOT NULL COMMENT '策略内容（JSON格式）',
  `description` varchar(500) DEFAULT NULL COMMENT '策略描述',
  `type` varchar(50) DEFAULT NULL COMMENT '策略类型（如：OSS, LOG等）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_upd_date` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_policy_name` (`name`)
);
```

## 初始化数据

1. **执行SQL脚本**：运行 `src/main/resources/sql/policy_table.sql` 创建表和初始数据
2. **自动初始化**：应用启动时会自动从策略文件导入到数据库（如果配置启用）
3. **手动初始化**：调用 `POST /api/policy/initialize` 接口

## API接口

### 策略管理接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/policy/list` | 获取所有策略 |
| GET | `/api/policy/enabled` | 获取启用的策略 |
| GET | `/api/policy/{id}` | 根据ID获取策略 |
| GET | `/api/policy/content/{name}` | 根据名称获取策略内容 |
| POST | `/api/policy/create` | 创建新策略 |
| PUT | `/api/policy/{id}` | 更新策略 |
| PUT | `/api/policy/{id}/enable` | 启用策略 |
| PUT | `/api/policy/{id}/disable` | 禁用策略 |
| DELETE | `/api/policy/{id}` | 删除策略 |
| POST | `/api/policy/validate` | 验证策略格式 |
| POST | `/api/policy/initialize` | 初始化默认策略 |

### STS Token接口

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/sts/token` | 获取STS Token |

## 使用方式

### 1. 在STS Token请求中使用策略

#### 方式一：使用策略名称
```json
{
  "token": "demo-token-123",
  "roleArn": "acs:ram::1653466991022848:role/ramslsuser",
  "policy": "oss_read_policy",
  "durationSeconds": 3600
}
```

#### 方式二：直接传入JSON策略
```json
{
  "token": "demo-token-123",
  "roleArn": "acs:ram::1653466991022848:role/ramslsuser",
  "policy": "{\"Statement\":[{\"Action\":[\"oss:GetObject\"],\"Effect\":\"Allow\",\"Resource\":[\"acs:oss:*:*:*\"]}],\"Version\":\"1\"}",
  "durationSeconds": 3600
}
```

### 2. 策略管理

#### 创建新策略
```bash
curl -X POST http://localhost:8080/api/policy/create \
  -H "Content-Type: application/json" \
  -d '{
    "name": "custom_policy",
    "displayName": "Custom Policy",
    "content": "{\"Statement\":[{\"Action\":[\"oss:GetObject\"],\"Effect\":\"Allow\",\"Resource\":[\"acs:oss:*:*:*\"]}],\"Version\":\"1\"}",
    "description": "自定义策略",
    "type": "CUSTOM",
    "status": 1
  }'
```

#### 获取策略列表
```bash
curl http://localhost:8080/api/policy/list
```

#### 根据名称获取策略内容
```bash
curl http://localhost:8080/api/policy/content/oss_read_policy
```

## 默认策略

系统预置了以下默认策略：

1. **oss_read_policy** - OSS读取权限
2. **oss_write_policy** - OSS写入权限  
3. **read_policy** - 日志读取权限
4. **write_policy** - 日志写入权限

## 配置说明

在 `application.yaml` 中：

```yaml
app:
  policy-initialization:
    enabled: true   # 是否在应用启动时初始化默认的策略数据
```

## 策略解析逻辑

1. 如果传入的policy参数以 `{` 开头，则认为是JSON字符串，直接使用
2. 否则认为是策略名称，从数据库中查找对应的启用策略
3. 如果找不到对应的策略，会抛出异常

## 缓存功能

系统为所有数据库查询添加了10分钟的Redis缓存，提高查询性能：

### 缓存管理接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/cache/stats` | 获取缓存统计信息 |
| GET | `/api/cache/health` | 获取缓存健康状态 |
| DELETE | `/api/cache/{cacheName}` | 清除指定缓存 |
| DELETE | `/api/cache/all` | 清除所有缓存 |
| DELETE | `/api/cache/policy` | 清除策略缓存 |
| DELETE | `/api/cache/policy/name/{policyName}` | 清除指定策略名称的缓存 |
| POST | `/api/cache/policy/warmup` | 预热策略缓存 |

### 缓存类型

1. **policy-cache** - 策略数据缓存（10分钟）
2. **aliyun-ak-cache** - 阿里云AK配置缓存（10分钟）
3. **sts-token-cache** - STS Token缓存（10分钟）

### 缓存特性

- **自动失效**：数据修改时自动清除相关缓存
- **空值处理**：不缓存null值，避免缓存穿透
- **错误处理**：缓存操作失败不影响业务逻辑
- **键命名**：使用前缀区分不同类型的缓存

### 缓存清除示例

```bash
# 清除所有策略缓存
curl -X DELETE http://localhost:8080/api/cache/policy

# 清除指定策略名称的缓存
curl -X DELETE http://localhost:8080/api/cache/policy/name/oss_read_policy

# 获取缓存统计
curl http://localhost:8080/api/cache/stats

# 获取缓存健康状态
curl http://localhost:8080/api/cache/health
```

## 注意事项

1. 策略名称必须唯一
2. 策略内容必须是有效的JSON格式
3. 只有启用状态的策略才能被使用
4. 策略内容必须包含 `Statement` 和 `Version` 字段
5. 缓存会在数据修改时自动清除，确保数据一致性
6. 缓存失效时间为10分钟，可根据需要调整
